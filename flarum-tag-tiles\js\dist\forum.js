(function(ge,X,ve){"use strict";function we(i){return i!==null&&typeof i=="object"&&"constructor"in i&&i.constructor===Object}function re(i,e){i===void 0&&(i={}),e===void 0&&(e={});const t=["__proto__","constructor","prototype"];Object.keys(e).filter(s=>t.indexOf(s)<0).forEach(s=>{typeof i[s]>"u"?i[s]=e[s]:we(e[s])&&we(i[s])&&Object.keys(e[s]).length>0&&re(i[s],e[s])})}const Te={body:{},addEventListener(){},removeEventListener(){},activeElement:{blur(){},nodeName:""},querySelector(){return null},querySelectorAll(){return[]},getElementById(){return null},createEvent(){return{initEvent(){}}},createElement(){return{children:[],childNodes:[],style:{},setAttribute(){},getElementsByTagName(){return[]}}},createElementNS(){return{}},importNode(){return null},location:{hash:"",host:"",hostname:"",href:"",origin:"",pathname:"",protocol:"",search:""}};function R(){const i=typeof document<"u"?document:{};return re(i,Te),i}const $e={document:Te,navigator:{userAgent:""},location:{hash:"",host:"",hostname:"",href:"",origin:"",pathname:"",protocol:"",search:""},history:{replaceState(){},pushState(){},go(){},back(){}},CustomEvent:function(){return this},addEventListener(){},removeEventListener(){},getComputedStyle(){return{getPropertyValue(){return""}}},Image(){},Date(){},screen:{},setTimeout(){},clearTimeout(){},matchMedia(){return{}},requestAnimationFrame(i){return typeof setTimeout>"u"?(i(),null):setTimeout(i,0)},cancelAnimationFrame(i){typeof setTimeout>"u"||clearTimeout(i)}};function z(){const i=typeof window<"u"?window:{};return re(i,$e),i}function We(i){return i===void 0&&(i=""),i.trim().split(" ").filter(e=>!!e.trim())}function He(i){const e=i;Object.keys(e).forEach(t=>{try{e[t]=null}catch{}try{delete e[t]}catch{}})}function Se(i,e){return e===void 0&&(e=0),setTimeout(i,e)}function Q(){return Date.now()}function Ye(i){const e=z();let t;return e.getComputedStyle&&(t=e.getComputedStyle(i,null)),!t&&i.currentStyle&&(t=i.currentStyle),t||(t=i.style),t}function Xe(i,e){e===void 0&&(e="x");const t=z();let s,r,n;const o=Ye(i);return t.WebKitCSSMatrix?(r=o.transform||o.webkitTransform,r.split(",").length>6&&(r=r.split(", ").map(a=>a.replace(",",".")).join(", ")),n=new t.WebKitCSSMatrix(r==="none"?"":r)):(n=o.MozTransform||o.OTransform||o.MsTransform||o.msTransform||o.transform||o.getPropertyValue("transform").replace("translate(","matrix(1, 0, 0, 1,"),s=n.toString().split(",")),e==="x"&&(t.WebKitCSSMatrix?r=n.m41:s.length===16?r=parseFloat(s[12]):r=parseFloat(s[4])),e==="y"&&(t.WebKitCSSMatrix?r=n.m42:s.length===16?r=parseFloat(s[13]):r=parseFloat(s[5])),r||0}function J(i){return typeof i=="object"&&i!==null&&i.constructor&&Object.prototype.toString.call(i).slice(8,-1)==="Object"}function je(i){return typeof window<"u"&&typeof window.HTMLElement<"u"?i instanceof HTMLElement:i&&(i.nodeType===1||i.nodeType===11)}function G(){const i=Object(arguments.length<=0?void 0:arguments[0]),e=["__proto__","constructor","prototype"];for(let t=1;t<arguments.length;t+=1){const s=t<0||arguments.length<=t?void 0:arguments[t];if(s!=null&&!je(s)){const r=Object.keys(Object(s)).filter(n=>e.indexOf(n)<0);for(let n=0,o=r.length;n<o;n+=1){const a=r[n],d=Object.getOwnPropertyDescriptor(s,a);d!==void 0&&d.enumerable&&(J(i[a])&&J(s[a])?s[a].__swiper__?i[a]=s[a]:G(i[a],s[a]):!J(i[a])&&J(s[a])?(i[a]={},s[a].__swiper__?i[a]=s[a]:G(i[a],s[a])):i[a]=s[a])}}}return i}function Z(i,e,t){i.style.setProperty(e,t)}function ye(i){let{swiper:e,targetPosition:t,side:s}=i;const r=z(),n=-e.translate;let o=null,a;const d=e.params.speed;e.wrapperEl.style.scrollSnapType="none",r.cancelAnimationFrame(e.cssModeFrameID);const l=t>n?"next":"prev",c=(f,m)=>l==="next"&&f>=m||l==="prev"&&f<=m,u=()=>{a=new Date().getTime(),o===null&&(o=a);const f=Math.max(Math.min((a-o)/d,1),0),m=.5-Math.cos(f*Math.PI)/2;let p=n+m*(t-n);if(c(p,t)&&(p=t),e.wrapperEl.scrollTo({[s]:p}),c(p,t)){e.wrapperEl.style.overflow="hidden",e.wrapperEl.style.scrollSnapType="",setTimeout(()=>{e.wrapperEl.style.overflow="",e.wrapperEl.scrollTo({[s]:p})}),r.cancelAnimationFrame(e.cssModeFrameID);return}e.cssModeFrameID=r.requestAnimationFrame(u)};u()}function H(i,e){e===void 0&&(e="");const t=z(),s=[...i.children];return t.HTMLSlotElement&&i instanceof HTMLSlotElement&&s.push(...i.assignedElements()),e?s.filter(r=>r.matches(e)):s}function qe(i,e){const t=[e];for(;t.length>0;){const s=t.shift();if(i===s)return!0;t.push(...s.children,...s.shadowRoot?s.shadowRoot.children:[],...s.assignedElements?s.assignedElements():[])}}function Ue(i,e){const t=z();let s=e.contains(i);return!s&&t.HTMLSlotElement&&e instanceof HTMLSlotElement&&(s=[...e.assignedElements()].includes(i),s||(s=qe(i,e))),s}function ee(i){try{console.warn(i);return}catch{}}function ne(i,e){e===void 0&&(e=[]);const t=document.createElement(i);return t.classList.add(...Array.isArray(e)?e:We(e)),t}function Ke(i,e){const t=[];for(;i.previousElementSibling;){const s=i.previousElementSibling;e?s.matches(e)&&t.push(s):t.push(s),i=s}return t}function Qe(i,e){const t=[];for(;i.nextElementSibling;){const s=i.nextElementSibling;e?s.matches(e)&&t.push(s):t.push(s),i=s}return t}function Y(i,e){return z().getComputedStyle(i,null).getPropertyValue(e)}function Ee(i){let e=i,t;if(e){for(t=0;(e=e.previousSibling)!==null;)e.nodeType===1&&(t+=1);return t}}function Je(i,e){const t=[];let s=i.parentElement;for(;s;)t.push(s),s=s.parentElement;return t}function be(i,e,t){const s=z();return i[e==="width"?"offsetWidth":"offsetHeight"]+parseFloat(s.getComputedStyle(i,null).getPropertyValue(e==="width"?"margin-right":"margin-top"))+parseFloat(s.getComputedStyle(i,null).getPropertyValue(e==="width"?"margin-left":"margin-bottom"))}let ae;function Ze(){const i=z(),e=R();return{smoothScroll:e.documentElement&&e.documentElement.style&&"scrollBehavior"in e.documentElement.style,touch:!!("ontouchstart"in i||i.DocumentTouch&&e instanceof i.DocumentTouch)}}function xe(){return ae||(ae=Ze()),ae}let oe;function et(i){let{userAgent:e}=i===void 0?{}:i;const t=xe(),s=z(),r=s.navigator.platform,n=e||s.navigator.userAgent,o={ios:!1,android:!1},a=s.screen.width,d=s.screen.height,l=n.match(/(Android);?[\s\/]+([\d.]+)?/);let c=n.match(/(iPad).*OS\s([\d_]+)/);const u=n.match(/(iPod)(.*OS\s([\d_]+))?/),f=!c&&n.match(/(iPhone\sOS|iOS)\s([\d_]+)/),m=r==="Win32";let p=r==="MacIntel";const w=["1024x1366","1366x1024","834x1194","1194x834","834x1112","1112x834","768x1024","1024x768","820x1180","1180x820","810x1080","1080x810"];return!c&&p&&t.touch&&w.indexOf(`${a}x${d}`)>=0&&(c=n.match(/(Version)\/([\d.]+)/),c||(c=[0,1,"13_0_0"]),p=!1),l&&!m&&(o.os="android",o.android=!0),(c||f||u)&&(o.os="ios",o.ios=!0),o}function Ie(i){return i===void 0&&(i={}),oe||(oe=et(i)),oe}let le;function tt(){const i=z(),e=Ie();let t=!1;function s(){const a=i.navigator.userAgent.toLowerCase();return a.indexOf("safari")>=0&&a.indexOf("chrome")<0&&a.indexOf("android")<0}if(s()){const a=String(i.navigator.userAgent);if(a.includes("Version/")){const[d,l]=a.split("Version/")[1].split(" ")[0].split(".").map(c=>Number(c));t=d<16||d===16&&l<2}}const r=/(iPhone|iPod|iPad).*AppleWebKit(?!.*Safari)/i.test(i.navigator.userAgent),n=s(),o=n||r&&e.ios;return{isSafari:t||n,needPerspectiveFix:t,need3dFix:o,isWebView:r}}function Pe(){return le||(le=tt()),le}function it(i){let{swiper:e,on:t,emit:s}=i;const r=z();let n=null,o=null;const a=()=>{!e||e.destroyed||!e.initialized||(s("beforeResize"),s("resize"))},d=()=>{!e||e.destroyed||!e.initialized||(n=new ResizeObserver(u=>{o=r.requestAnimationFrame(()=>{const{width:f,height:m}=e;let p=f,w=m;u.forEach(I=>{let{contentBoxSize:h,contentRect:T,target:v}=I;v&&v!==e.el||(p=T?T.width:(h[0]||h).inlineSize,w=T?T.height:(h[0]||h).blockSize)}),(p!==f||w!==m)&&a()})}),n.observe(e.el))},l=()=>{o&&r.cancelAnimationFrame(o),n&&n.unobserve&&e.el&&(n.unobserve(e.el),n=null)},c=()=>{!e||e.destroyed||!e.initialized||s("orientationchange")};t("init",()=>{if(e.params.resizeObserver&&typeof r.ResizeObserver<"u"){d();return}r.addEventListener("resize",a),r.addEventListener("orientationchange",c)}),t("destroy",()=>{l(),r.removeEventListener("resize",a),r.removeEventListener("orientationchange",c)})}function st(i){let{swiper:e,extendParams:t,on:s,emit:r}=i;const n=[],o=z(),a=function(c,u){u===void 0&&(u={});const f=o.MutationObserver||o.WebkitMutationObserver,m=new f(p=>{if(e.__preventObserver__)return;if(p.length===1){r("observerUpdate",p[0]);return}const w=function(){r("observerUpdate",p[0])};o.requestAnimationFrame?o.requestAnimationFrame(w):o.setTimeout(w,0)});m.observe(c,{attributes:typeof u.attributes>"u"?!0:u.attributes,childList:e.isElement||(typeof u.childList>"u"?!0:u).childList,characterData:typeof u.characterData>"u"?!0:u.characterData}),n.push(m)},d=()=>{if(e.params.observer){if(e.params.observeParents){const c=Je(e.hostEl);for(let u=0;u<c.length;u+=1)a(c[u])}a(e.hostEl,{childList:e.params.observeSlideChildren}),a(e.wrapperEl,{attributes:!1})}},l=()=>{n.forEach(c=>{c.disconnect()}),n.splice(0,n.length)};t({observer:!1,observeParents:!1,observeSlideChildren:!1}),s("init",d),s("destroy",l)}var rt={on(i,e,t){const s=this;if(!s.eventsListeners||s.destroyed||typeof e!="function")return s;const r=t?"unshift":"push";return i.split(" ").forEach(n=>{s.eventsListeners[n]||(s.eventsListeners[n]=[]),s.eventsListeners[n][r](e)}),s},once(i,e,t){const s=this;if(!s.eventsListeners||s.destroyed||typeof e!="function")return s;function r(){s.off(i,r),r.__emitterProxy&&delete r.__emitterProxy;for(var n=arguments.length,o=new Array(n),a=0;a<n;a++)o[a]=arguments[a];e.apply(s,o)}return r.__emitterProxy=e,s.on(i,r,t)},onAny(i,e){const t=this;if(!t.eventsListeners||t.destroyed||typeof i!="function")return t;const s=e?"unshift":"push";return t.eventsAnyListeners.indexOf(i)<0&&t.eventsAnyListeners[s](i),t},offAny(i){const e=this;if(!e.eventsListeners||e.destroyed||!e.eventsAnyListeners)return e;const t=e.eventsAnyListeners.indexOf(i);return t>=0&&e.eventsAnyListeners.splice(t,1),e},off(i,e){const t=this;return!t.eventsListeners||t.destroyed||!t.eventsListeners||i.split(" ").forEach(s=>{typeof e>"u"?t.eventsListeners[s]=[]:t.eventsListeners[s]&&t.eventsListeners[s].forEach((r,n)=>{(r===e||r.__emitterProxy&&r.__emitterProxy===e)&&t.eventsListeners[s].splice(n,1)})}),t},emit(){const i=this;if(!i.eventsListeners||i.destroyed||!i.eventsListeners)return i;let e,t,s;for(var r=arguments.length,n=new Array(r),o=0;o<r;o++)n[o]=arguments[o];return typeof n[0]=="string"||Array.isArray(n[0])?(e=n[0],t=n.slice(1,n.length),s=i):(e=n[0].events,t=n[0].data,s=n[0].context||i),t.unshift(s),(Array.isArray(e)?e:e.split(" ")).forEach(d=>{i.eventsAnyListeners&&i.eventsAnyListeners.length&&i.eventsAnyListeners.forEach(l=>{l.apply(s,[d,...t])}),i.eventsListeners&&i.eventsListeners[d]&&i.eventsListeners[d].forEach(l=>{l.apply(s,t)})}),i}};function nt(){const i=this;let e,t;const s=i.el;typeof i.params.width<"u"&&i.params.width!==null?e=i.params.width:e=s.clientWidth,typeof i.params.height<"u"&&i.params.height!==null?t=i.params.height:t=s.clientHeight,!(e===0&&i.isHorizontal()||t===0&&i.isVertical())&&(e=e-parseInt(Y(s,"padding-left")||0,10)-parseInt(Y(s,"padding-right")||0,10),t=t-parseInt(Y(s,"padding-top")||0,10)-parseInt(Y(s,"padding-bottom")||0,10),Number.isNaN(e)&&(e=0),Number.isNaN(t)&&(t=0),Object.assign(i,{width:e,height:t,size:i.isHorizontal()?e:t}))}function at(){const i=this;function e(g,y){return parseFloat(g.getPropertyValue(i.getDirectionLabel(y))||0)}const t=i.params,{wrapperEl:s,slidesEl:r,size:n,rtlTranslate:o,wrongRTL:a}=i,d=i.virtual&&t.virtual.enabled,l=d?i.virtual.slides.length:i.slides.length,c=H(r,`.${i.params.slideClass}, swiper-slide`),u=d?i.virtual.slides.length:c.length;let f=[];const m=[],p=[];let w=t.slidesOffsetBefore;typeof w=="function"&&(w=t.slidesOffsetBefore.call(i));let I=t.slidesOffsetAfter;typeof I=="function"&&(I=t.slidesOffsetAfter.call(i));const h=i.snapGrid.length,T=i.slidesGrid.length;let v=t.spaceBetween,E=-w,S=0,L=0;if(typeof n>"u")return;typeof v=="string"&&v.indexOf("%")>=0?v=parseFloat(v.replace("%",""))/100*n:typeof v=="string"&&(v=parseFloat(v)),i.virtualSize=-v,c.forEach(g=>{o?g.style.marginLeft="":g.style.marginRight="",g.style.marginBottom="",g.style.marginTop=""}),t.centeredSlides&&t.cssMode&&(Z(s,"--swiper-centered-offset-before",""),Z(s,"--swiper-centered-offset-after",""));const C=t.grid&&t.grid.rows>1&&i.grid;C?i.grid.initSlides(c):i.grid&&i.grid.unsetSlides();let x;const O=t.slidesPerView==="auto"&&t.breakpoints&&Object.keys(t.breakpoints).filter(g=>typeof t.breakpoints[g].slidesPerView<"u").length>0;for(let g=0;g<u;g+=1){x=0;let y;if(c[g]&&(y=c[g]),C&&i.grid.updateSlide(g,y,c),!(c[g]&&Y(y,"display")==="none")){if(t.slidesPerView==="auto"){O&&(c[g].style[i.getDirectionLabel("width")]="");const P=getComputedStyle(y),A=y.style.transform,$=y.style.webkitTransform;if(A&&(y.style.transform="none"),$&&(y.style.webkitTransform="none"),t.roundLengths)x=i.isHorizontal()?be(y,"width"):be(y,"height");else{const W=e(P,"width"),M=e(P,"padding-left"),D=e(P,"padding-right"),b=e(P,"margin-left"),_=e(P,"margin-right"),k=P.getPropertyValue("box-sizing");if(k&&k==="border-box")x=W+b+_;else{const{clientWidth:j,offsetWidth:se}=y;x=W+M+D+b+_+(se-j)}}A&&(y.style.transform=A),$&&(y.style.webkitTransform=$),t.roundLengths&&(x=Math.floor(x))}else x=(n-(t.slidesPerView-1)*v)/t.slidesPerView,t.roundLengths&&(x=Math.floor(x)),c[g]&&(c[g].style[i.getDirectionLabel("width")]=`${x}px`);c[g]&&(c[g].swiperSlideSize=x),p.push(x),t.centeredSlides?(E=E+x/2+S/2+v,S===0&&g!==0&&(E=E-n/2-v),g===0&&(E=E-n/2-v),Math.abs(E)<1/1e3&&(E=0),t.roundLengths&&(E=Math.floor(E)),L%t.slidesPerGroup===0&&f.push(E),m.push(E)):(t.roundLengths&&(E=Math.floor(E)),(L-Math.min(i.params.slidesPerGroupSkip,L))%i.params.slidesPerGroup===0&&f.push(E),m.push(E),E=E+x+v),i.virtualSize+=x+v,S=x,L+=1}}if(i.virtualSize=Math.max(i.virtualSize,n)+I,o&&a&&(t.effect==="slide"||t.effect==="coverflow")&&(s.style.width=`${i.virtualSize+v}px`),t.setWrapperSize&&(s.style[i.getDirectionLabel("width")]=`${i.virtualSize+v}px`),C&&i.grid.updateWrapperSize(x,f),!t.centeredSlides){const g=[];for(let y=0;y<f.length;y+=1){let P=f[y];t.roundLengths&&(P=Math.floor(P)),f[y]<=i.virtualSize-n&&g.push(P)}f=g,Math.floor(i.virtualSize-n)-Math.floor(f[f.length-1])>1&&f.push(i.virtualSize-n)}if(d&&t.loop){const g=p[0]+v;if(t.slidesPerGroup>1){const y=Math.ceil((i.virtual.slidesBefore+i.virtual.slidesAfter)/t.slidesPerGroup),P=g*t.slidesPerGroup;for(let A=0;A<y;A+=1)f.push(f[f.length-1]+P)}for(let y=0;y<i.virtual.slidesBefore+i.virtual.slidesAfter;y+=1)t.slidesPerGroup===1&&f.push(f[f.length-1]+g),m.push(m[m.length-1]+g),i.virtualSize+=g}if(f.length===0&&(f=[0]),v!==0){const g=i.isHorizontal()&&o?"marginLeft":i.getDirectionLabel("marginRight");c.filter((y,P)=>!t.cssMode||t.loop?!0:P!==c.length-1).forEach(y=>{y.style[g]=`${v}px`})}if(t.centeredSlides&&t.centeredSlidesBounds){let g=0;p.forEach(P=>{g+=P+(v||0)}),g-=v;const y=g>n?g-n:0;f=f.map(P=>P<=0?-w:P>y?y+I:P)}if(t.centerInsufficientSlides){let g=0;p.forEach(P=>{g+=P+(v||0)}),g-=v;const y=(t.slidesOffsetBefore||0)+(t.slidesOffsetAfter||0);if(g+y<n){const P=(n-g-y)/2;f.forEach((A,$)=>{f[$]=A-P}),m.forEach((A,$)=>{m[$]=A+P})}}if(Object.assign(i,{slides:c,snapGrid:f,slidesGrid:m,slidesSizesGrid:p}),t.centeredSlides&&t.cssMode&&!t.centeredSlidesBounds){Z(s,"--swiper-centered-offset-before",`${-f[0]}px`),Z(s,"--swiper-centered-offset-after",`${i.size/2-p[p.length-1]/2}px`);const g=-i.snapGrid[0],y=-i.slidesGrid[0];i.snapGrid=i.snapGrid.map(P=>P+g),i.slidesGrid=i.slidesGrid.map(P=>P+y)}if(u!==l&&i.emit("slidesLengthChange"),f.length!==h&&(i.params.watchOverflow&&i.checkOverflow(),i.emit("snapGridLengthChange")),m.length!==T&&i.emit("slidesGridLengthChange"),t.watchSlidesProgress&&i.updateSlidesOffset(),i.emit("slidesUpdated"),!d&&!t.cssMode&&(t.effect==="slide"||t.effect==="fade")){const g=`${t.containerModifierClass}backface-hidden`,y=i.el.classList.contains(g);u<=t.maxBackfaceHiddenSlides?y||i.el.classList.add(g):y&&i.el.classList.remove(g)}}function ot(i){const e=this,t=[],s=e.virtual&&e.params.virtual.enabled;let r=0,n;typeof i=="number"?e.setTransition(i):i===!0&&e.setTransition(e.params.speed);const o=a=>s?e.slides[e.getSlideIndexByData(a)]:e.slides[a];if(e.params.slidesPerView!=="auto"&&e.params.slidesPerView>1)if(e.params.centeredSlides)(e.visibleSlides||[]).forEach(a=>{t.push(a)});else for(n=0;n<Math.ceil(e.params.slidesPerView);n+=1){const a=e.activeIndex+n;if(a>e.slides.length&&!s)break;t.push(o(a))}else t.push(o(e.activeIndex));for(n=0;n<t.length;n+=1)if(typeof t[n]<"u"){const a=t[n].offsetHeight;r=a>r?a:r}(r||r===0)&&(e.wrapperEl.style.height=`${r}px`)}function lt(){const i=this,e=i.slides,t=i.isElement?i.isHorizontal()?i.wrapperEl.offsetLeft:i.wrapperEl.offsetTop:0;for(let s=0;s<e.length;s+=1)e[s].swiperSlideOffset=(i.isHorizontal()?e[s].offsetLeft:e[s].offsetTop)-t-i.cssOverflowAdjustment()}const Me=(i,e,t)=>{e&&!i.classList.contains(t)?i.classList.add(t):!e&&i.classList.contains(t)&&i.classList.remove(t)};function dt(i){i===void 0&&(i=this&&this.translate||0);const e=this,t=e.params,{slides:s,rtlTranslate:r,snapGrid:n}=e;if(s.length===0)return;typeof s[0].swiperSlideOffset>"u"&&e.updateSlidesOffset();let o=-i;r&&(o=i),e.visibleSlidesIndexes=[],e.visibleSlides=[];let a=t.spaceBetween;typeof a=="string"&&a.indexOf("%")>=0?a=parseFloat(a.replace("%",""))/100*e.size:typeof a=="string"&&(a=parseFloat(a));for(let d=0;d<s.length;d+=1){const l=s[d];let c=l.swiperSlideOffset;t.cssMode&&t.centeredSlides&&(c-=s[0].swiperSlideOffset);const u=(o+(t.centeredSlides?e.minTranslate():0)-c)/(l.swiperSlideSize+a),f=(o-n[0]+(t.centeredSlides?e.minTranslate():0)-c)/(l.swiperSlideSize+a),m=-(o-c),p=m+e.slidesSizesGrid[d],w=m>=0&&m<=e.size-e.slidesSizesGrid[d],I=m>=0&&m<e.size-1||p>1&&p<=e.size||m<=0&&p>=e.size;I&&(e.visibleSlides.push(l),e.visibleSlidesIndexes.push(d)),Me(l,I,t.slideVisibleClass),Me(l,w,t.slideFullyVisibleClass),l.progress=r?-u:u,l.originalProgress=r?-f:f}}function ct(i){const e=this;if(typeof i>"u"){const c=e.rtlTranslate?-1:1;i=e&&e.translate&&e.translate*c||0}const t=e.params,s=e.maxTranslate()-e.minTranslate();let{progress:r,isBeginning:n,isEnd:o,progressLoop:a}=e;const d=n,l=o;if(s===0)r=0,n=!0,o=!0;else{r=(i-e.minTranslate())/s;const c=Math.abs(i-e.minTranslate())<1,u=Math.abs(i-e.maxTranslate())<1;n=c||r<=0,o=u||r>=1,c&&(r=0),u&&(r=1)}if(t.loop){const c=e.getSlideIndexByData(0),u=e.getSlideIndexByData(e.slides.length-1),f=e.slidesGrid[c],m=e.slidesGrid[u],p=e.slidesGrid[e.slidesGrid.length-1],w=Math.abs(i);w>=f?a=(w-f)/p:a=(w+p-m)/p,a>1&&(a-=1)}Object.assign(e,{progress:r,progressLoop:a,isBeginning:n,isEnd:o}),(t.watchSlidesProgress||t.centeredSlides&&t.autoHeight)&&e.updateSlidesProgress(i),n&&!d&&e.emit("reachBeginning toEdge"),o&&!l&&e.emit("reachEnd toEdge"),(d&&!n||l&&!o)&&e.emit("fromEdge"),e.emit("progress",r)}const de=(i,e,t)=>{e&&!i.classList.contains(t)?i.classList.add(t):!e&&i.classList.contains(t)&&i.classList.remove(t)};function ut(){const i=this,{slides:e,params:t,slidesEl:s,activeIndex:r}=i,n=i.virtual&&t.virtual.enabled,o=i.grid&&t.grid&&t.grid.rows>1,a=u=>H(s,`.${t.slideClass}${u}, swiper-slide${u}`)[0];let d,l,c;if(n)if(t.loop){let u=r-i.virtual.slidesBefore;u<0&&(u=i.virtual.slides.length+u),u>=i.virtual.slides.length&&(u-=i.virtual.slides.length),d=a(`[data-swiper-slide-index="${u}"]`)}else d=a(`[data-swiper-slide-index="${r}"]`);else o?(d=e.find(u=>u.column===r),c=e.find(u=>u.column===r+1),l=e.find(u=>u.column===r-1)):d=e[r];d&&(o||(c=Qe(d,`.${t.slideClass}, swiper-slide`)[0],t.loop&&!c&&(c=e[0]),l=Ke(d,`.${t.slideClass}, swiper-slide`)[0],t.loop&&!l===0&&(l=e[e.length-1]))),e.forEach(u=>{de(u,u===d,t.slideActiveClass),de(u,u===c,t.slideNextClass),de(u,u===l,t.slidePrevClass)}),i.emitSlidesClasses()}const te=(i,e)=>{if(!i||i.destroyed||!i.params)return;const t=()=>i.isElement?"swiper-slide":`.${i.params.slideClass}`,s=e.closest(t());if(s){let r=s.querySelector(`.${i.params.lazyPreloaderClass}`);!r&&i.isElement&&(s.shadowRoot?r=s.shadowRoot.querySelector(`.${i.params.lazyPreloaderClass}`):requestAnimationFrame(()=>{s.shadowRoot&&(r=s.shadowRoot.querySelector(`.${i.params.lazyPreloaderClass}`),r&&r.remove())})),r&&r.remove()}},ce=(i,e)=>{if(!i.slides[e])return;const t=i.slides[e].querySelector('[loading="lazy"]');t&&t.removeAttribute("loading")},ue=i=>{if(!i||i.destroyed||!i.params)return;let e=i.params.lazyPreloadPrevNext;const t=i.slides.length;if(!t||!e||e<0)return;e=Math.min(e,t);const s=i.params.slidesPerView==="auto"?i.slidesPerViewDynamic():Math.ceil(i.params.slidesPerView),r=i.activeIndex;if(i.params.grid&&i.params.grid.rows>1){const o=r,a=[o-e];a.push(...Array.from({length:e}).map((d,l)=>o+s+l)),i.slides.forEach((d,l)=>{a.includes(d.column)&&ce(i,l)});return}const n=r+s-1;if(i.params.rewind||i.params.loop)for(let o=r-e;o<=n+e;o+=1){const a=(o%t+t)%t;(a<r||a>n)&&ce(i,a)}else for(let o=Math.max(r-e,0);o<=Math.min(n+e,t-1);o+=1)o!==r&&(o>n||o<r)&&ce(i,o)};function ft(i){const{slidesGrid:e,params:t}=i,s=i.rtlTranslate?i.translate:-i.translate;let r;for(let n=0;n<e.length;n+=1)typeof e[n+1]<"u"?s>=e[n]&&s<e[n+1]-(e[n+1]-e[n])/2?r=n:s>=e[n]&&s<e[n+1]&&(r=n+1):s>=e[n]&&(r=n);return t.normalizeSlideIndex&&(r<0||typeof r>"u")&&(r=0),r}function pt(i){const e=this,t=e.rtlTranslate?e.translate:-e.translate,{snapGrid:s,params:r,activeIndex:n,realIndex:o,snapIndex:a}=e;let d=i,l;const c=m=>{let p=m-e.virtual.slidesBefore;return p<0&&(p=e.virtual.slides.length+p),p>=e.virtual.slides.length&&(p-=e.virtual.slides.length),p};if(typeof d>"u"&&(d=ft(e)),s.indexOf(t)>=0)l=s.indexOf(t);else{const m=Math.min(r.slidesPerGroupSkip,d);l=m+Math.floor((d-m)/r.slidesPerGroup)}if(l>=s.length&&(l=s.length-1),d===n&&!e.params.loop){l!==a&&(e.snapIndex=l,e.emit("snapIndexChange"));return}if(d===n&&e.params.loop&&e.virtual&&e.params.virtual.enabled){e.realIndex=c(d);return}const u=e.grid&&r.grid&&r.grid.rows>1;let f;if(e.virtual&&r.virtual.enabled&&r.loop)f=c(d);else if(u){const m=e.slides.find(w=>w.column===d);let p=parseInt(m.getAttribute("data-swiper-slide-index"),10);Number.isNaN(p)&&(p=Math.max(e.slides.indexOf(m),0)),f=Math.floor(p/r.grid.rows)}else if(e.slides[d]){const m=e.slides[d].getAttribute("data-swiper-slide-index");m?f=parseInt(m,10):f=d}else f=d;Object.assign(e,{previousSnapIndex:a,snapIndex:l,previousRealIndex:o,realIndex:f,previousIndex:n,activeIndex:d}),e.initialized&&ue(e),e.emit("activeIndexChange"),e.emit("snapIndexChange"),(e.initialized||e.params.runCallbacksOnInit)&&(o!==f&&e.emit("realIndexChange"),e.emit("slideChange"))}function mt(i,e){const t=this,s=t.params;let r=i.closest(`.${s.slideClass}, swiper-slide`);!r&&t.isElement&&e&&e.length>1&&e.includes(i)&&[...e.slice(e.indexOf(i)+1,e.length)].forEach(a=>{!r&&a.matches&&a.matches(`.${s.slideClass}, swiper-slide`)&&(r=a)});let n=!1,o;if(r){for(let a=0;a<t.slides.length;a+=1)if(t.slides[a]===r){n=!0,o=a;break}}if(r&&n)t.clickedSlide=r,t.virtual&&t.params.virtual.enabled?t.clickedIndex=parseInt(r.getAttribute("data-swiper-slide-index"),10):t.clickedIndex=o;else{t.clickedSlide=void 0,t.clickedIndex=void 0;return}s.slideToClickedSlide&&t.clickedIndex!==void 0&&t.clickedIndex!==t.activeIndex&&t.slideToClickedSlide()}var ht={updateSize:nt,updateSlides:at,updateAutoHeight:ot,updateSlidesOffset:lt,updateSlidesProgress:dt,updateProgress:ct,updateSlidesClasses:ut,updateActiveIndex:pt,updateClickedSlide:mt};function gt(i){i===void 0&&(i=this.isHorizontal()?"x":"y");const e=this,{params:t,rtlTranslate:s,translate:r,wrapperEl:n}=e;if(t.virtualTranslate)return s?-r:r;if(t.cssMode)return r;let o=Xe(n,i);return o+=e.cssOverflowAdjustment(),s&&(o=-o),o||0}function vt(i,e){const t=this,{rtlTranslate:s,params:r,wrapperEl:n,progress:o}=t;let a=0,d=0;const l=0;t.isHorizontal()?a=s?-i:i:d=i,r.roundLengths&&(a=Math.floor(a),d=Math.floor(d)),t.previousTranslate=t.translate,t.translate=t.isHorizontal()?a:d,r.cssMode?n[t.isHorizontal()?"scrollLeft":"scrollTop"]=t.isHorizontal()?-a:-d:r.virtualTranslate||(t.isHorizontal()?a-=t.cssOverflowAdjustment():d-=t.cssOverflowAdjustment(),n.style.transform=`translate3d(${a}px, ${d}px, ${l}px)`);let c;const u=t.maxTranslate()-t.minTranslate();u===0?c=0:c=(i-t.minTranslate())/u,c!==o&&t.updateProgress(i),t.emit("setTranslate",t.translate,e)}function wt(){return-this.snapGrid[0]}function Tt(){return-this.snapGrid[this.snapGrid.length-1]}function St(i,e,t,s,r){i===void 0&&(i=0),e===void 0&&(e=this.params.speed),t===void 0&&(t=!0),s===void 0&&(s=!0);const n=this,{params:o,wrapperEl:a}=n;if(n.animating&&o.preventInteractionOnTransition)return!1;const d=n.minTranslate(),l=n.maxTranslate();let c;if(s&&i>d?c=d:s&&i<l?c=l:c=i,n.updateProgress(c),o.cssMode){const u=n.isHorizontal();if(e===0)a[u?"scrollLeft":"scrollTop"]=-c;else{if(!n.support.smoothScroll)return ye({swiper:n,targetPosition:-c,side:u?"left":"top"}),!0;a.scrollTo({[u?"left":"top"]:-c,behavior:"smooth"})}return!0}return e===0?(n.setTransition(0),n.setTranslate(c),t&&(n.emit("beforeTransitionStart",e,r),n.emit("transitionEnd"))):(n.setTransition(e),n.setTranslate(c),t&&(n.emit("beforeTransitionStart",e,r),n.emit("transitionStart")),n.animating||(n.animating=!0,n.onTranslateToWrapperTransitionEnd||(n.onTranslateToWrapperTransitionEnd=function(f){!n||n.destroyed||f.target===this&&(n.wrapperEl.removeEventListener("transitionend",n.onTranslateToWrapperTransitionEnd),n.onTranslateToWrapperTransitionEnd=null,delete n.onTranslateToWrapperTransitionEnd,n.animating=!1,t&&n.emit("transitionEnd"))}),n.wrapperEl.addEventListener("transitionend",n.onTranslateToWrapperTransitionEnd))),!0}var yt={getTranslate:gt,setTranslate:vt,minTranslate:wt,maxTranslate:Tt,translateTo:St};function Et(i,e){const t=this;t.params.cssMode||(t.wrapperEl.style.transitionDuration=`${i}ms`,t.wrapperEl.style.transitionDelay=i===0?"0ms":""),t.emit("setTransition",i,e)}function Ce(i){let{swiper:e,runCallbacks:t,direction:s,step:r}=i;const{activeIndex:n,previousIndex:o}=e;let a=s;a||(n>o?a="next":n<o?a="prev":a="reset"),e.emit(`transition${r}`),t&&a==="reset"?e.emit(`slideResetTransition${r}`):t&&n!==o&&(e.emit(`slideChangeTransition${r}`),a==="next"?e.emit(`slideNextTransition${r}`):e.emit(`slidePrevTransition${r}`))}function bt(i,e){i===void 0&&(i=!0);const t=this,{params:s}=t;s.cssMode||(s.autoHeight&&t.updateAutoHeight(),Ce({swiper:t,runCallbacks:i,direction:e,step:"Start"}))}function xt(i,e){i===void 0&&(i=!0);const t=this,{params:s}=t;t.animating=!1,!s.cssMode&&(t.setTransition(0),Ce({swiper:t,runCallbacks:i,direction:e,step:"End"}))}var It={setTransition:Et,transitionStart:bt,transitionEnd:xt};function Pt(i,e,t,s,r){i===void 0&&(i=0),t===void 0&&(t=!0),typeof i=="string"&&(i=parseInt(i,10));const n=this;let o=i;o<0&&(o=0);const{params:a,snapGrid:d,slidesGrid:l,previousIndex:c,activeIndex:u,rtlTranslate:f,wrapperEl:m,enabled:p}=n;if(!p&&!s&&!r||n.destroyed||n.animating&&a.preventInteractionOnTransition)return!1;typeof e>"u"&&(e=n.params.speed);const w=Math.min(n.params.slidesPerGroupSkip,o);let I=w+Math.floor((o-w)/n.params.slidesPerGroup);I>=d.length&&(I=d.length-1);const h=-d[I];if(a.normalizeSlideIndex)for(let C=0;C<l.length;C+=1){const x=-Math.floor(h*100),O=Math.floor(l[C]*100),g=Math.floor(l[C+1]*100);typeof l[C+1]<"u"?x>=O&&x<g-(g-O)/2?o=C:x>=O&&x<g&&(o=C+1):x>=O&&(o=C)}if(n.initialized&&o!==u&&(!n.allowSlideNext&&(f?h>n.translate&&h>n.minTranslate():h<n.translate&&h<n.minTranslate())||!n.allowSlidePrev&&h>n.translate&&h>n.maxTranslate()&&(u||0)!==o))return!1;o!==(c||0)&&t&&n.emit("beforeSlideChangeStart"),n.updateProgress(h);let T;o>u?T="next":o<u?T="prev":T="reset";const v=n.virtual&&n.params.virtual.enabled;if(!(v&&r)&&(f&&-h===n.translate||!f&&h===n.translate))return n.updateActiveIndex(o),a.autoHeight&&n.updateAutoHeight(),n.updateSlidesClasses(),a.effect!=="slide"&&n.setTranslate(h),T!=="reset"&&(n.transitionStart(t,T),n.transitionEnd(t,T)),!1;if(a.cssMode){const C=n.isHorizontal(),x=f?h:-h;if(e===0)v&&(n.wrapperEl.style.scrollSnapType="none",n._immediateVirtual=!0),v&&!n._cssModeVirtualInitialSet&&n.params.initialSlide>0?(n._cssModeVirtualInitialSet=!0,requestAnimationFrame(()=>{m[C?"scrollLeft":"scrollTop"]=x})):m[C?"scrollLeft":"scrollTop"]=x,v&&requestAnimationFrame(()=>{n.wrapperEl.style.scrollSnapType="",n._immediateVirtual=!1});else{if(!n.support.smoothScroll)return ye({swiper:n,targetPosition:x,side:C?"left":"top"}),!0;m.scrollTo({[C?"left":"top"]:x,behavior:"smooth"})}return!0}const L=Pe().isSafari;return v&&!r&&L&&n.isElement&&n.virtual.update(!1,!1,o),n.setTransition(e),n.setTranslate(h),n.updateActiveIndex(o),n.updateSlidesClasses(),n.emit("beforeTransitionStart",e,s),n.transitionStart(t,T),e===0?n.transitionEnd(t,T):n.animating||(n.animating=!0,n.onSlideToWrapperTransitionEnd||(n.onSlideToWrapperTransitionEnd=function(x){!n||n.destroyed||x.target===this&&(n.wrapperEl.removeEventListener("transitionend",n.onSlideToWrapperTransitionEnd),n.onSlideToWrapperTransitionEnd=null,delete n.onSlideToWrapperTransitionEnd,n.transitionEnd(t,T))}),n.wrapperEl.addEventListener("transitionend",n.onSlideToWrapperTransitionEnd)),!0}function Mt(i,e,t,s){i===void 0&&(i=0),t===void 0&&(t=!0),typeof i=="string"&&(i=parseInt(i,10));const r=this;if(r.destroyed)return;typeof e>"u"&&(e=r.params.speed);const n=r.grid&&r.params.grid&&r.params.grid.rows>1;let o=i;if(r.params.loop)if(r.virtual&&r.params.virtual.enabled)o=o+r.virtual.slidesBefore;else{let a;if(n){const f=o*r.params.grid.rows;a=r.slides.find(m=>m.getAttribute("data-swiper-slide-index")*1===f).column}else a=r.getSlideIndexByData(o);const d=n?Math.ceil(r.slides.length/r.params.grid.rows):r.slides.length,{centeredSlides:l}=r.params;let c=r.params.slidesPerView;c==="auto"?c=r.slidesPerViewDynamic():(c=Math.ceil(parseFloat(r.params.slidesPerView,10)),l&&c%2===0&&(c=c+1));let u=d-a<c;if(l&&(u=u||a<Math.ceil(c/2)),s&&l&&r.params.slidesPerView!=="auto"&&!n&&(u=!1),u){const f=l?a<r.activeIndex?"prev":"next":a-r.activeIndex-1<r.params.slidesPerView?"next":"prev";r.loopFix({direction:f,slideTo:!0,activeSlideIndex:f==="next"?a+1:a-d+1,slideRealIndex:f==="next"?r.realIndex:void 0})}if(n){const f=o*r.params.grid.rows;o=r.slides.find(m=>m.getAttribute("data-swiper-slide-index")*1===f).column}else o=r.getSlideIndexByData(o)}return requestAnimationFrame(()=>{r.slideTo(o,e,t,s)}),r}function Ct(i,e,t){e===void 0&&(e=!0);const s=this,{enabled:r,params:n,animating:o}=s;if(!r||s.destroyed)return s;typeof i>"u"&&(i=s.params.speed);let a=n.slidesPerGroup;n.slidesPerView==="auto"&&n.slidesPerGroup===1&&n.slidesPerGroupAuto&&(a=Math.max(s.slidesPerViewDynamic("current",!0),1));const d=s.activeIndex<n.slidesPerGroupSkip?1:a,l=s.virtual&&n.virtual.enabled;if(n.loop){if(o&&!l&&n.loopPreventsSliding)return!1;if(s.loopFix({direction:"next"}),s._clientLeft=s.wrapperEl.clientLeft,s.activeIndex===s.slides.length-1&&n.cssMode)return requestAnimationFrame(()=>{s.slideTo(s.activeIndex+d,i,e,t)}),!0}return n.rewind&&s.isEnd?s.slideTo(0,i,e,t):s.slideTo(s.activeIndex+d,i,e,t)}function Lt(i,e,t){e===void 0&&(e=!0);const s=this,{params:r,snapGrid:n,slidesGrid:o,rtlTranslate:a,enabled:d,animating:l}=s;if(!d||s.destroyed)return s;typeof i>"u"&&(i=s.params.speed);const c=s.virtual&&r.virtual.enabled;if(r.loop){if(l&&!c&&r.loopPreventsSliding)return!1;s.loopFix({direction:"prev"}),s._clientLeft=s.wrapperEl.clientLeft}const u=a?s.translate:-s.translate;function f(T){return T<0?-Math.floor(Math.abs(T)):Math.floor(T)}const m=f(u),p=n.map(T=>f(T)),w=r.freeMode&&r.freeMode.enabled;let I=n[p.indexOf(m)-1];if(typeof I>"u"&&(r.cssMode||w)){let T;n.forEach((v,E)=>{m>=v&&(T=E)}),typeof T<"u"&&(I=w?n[T]:n[T>0?T-1:T])}let h=0;if(typeof I<"u"&&(h=o.indexOf(I),h<0&&(h=s.activeIndex-1),r.slidesPerView==="auto"&&r.slidesPerGroup===1&&r.slidesPerGroupAuto&&(h=h-s.slidesPerViewDynamic("previous",!0)+1,h=Math.max(h,0))),r.rewind&&s.isBeginning){const T=s.params.virtual&&s.params.virtual.enabled&&s.virtual?s.virtual.slides.length-1:s.slides.length-1;return s.slideTo(T,i,e,t)}else if(r.loop&&s.activeIndex===0&&r.cssMode)return requestAnimationFrame(()=>{s.slideTo(h,i,e,t)}),!0;return s.slideTo(h,i,e,t)}function Ot(i,e,t){e===void 0&&(e=!0);const s=this;if(!s.destroyed)return typeof i>"u"&&(i=s.params.speed),s.slideTo(s.activeIndex,i,e,t)}function At(i,e,t,s){e===void 0&&(e=!0),s===void 0&&(s=.5);const r=this;if(r.destroyed)return;typeof i>"u"&&(i=r.params.speed);let n=r.activeIndex;const o=Math.min(r.params.slidesPerGroupSkip,n),a=o+Math.floor((n-o)/r.params.slidesPerGroup),d=r.rtlTranslate?r.translate:-r.translate;if(d>=r.snapGrid[a]){const l=r.snapGrid[a],c=r.snapGrid[a+1];d-l>(c-l)*s&&(n+=r.params.slidesPerGroup)}else{const l=r.snapGrid[a-1],c=r.snapGrid[a];d-l<=(c-l)*s&&(n-=r.params.slidesPerGroup)}return n=Math.max(n,0),n=Math.min(n,r.slidesGrid.length-1),r.slideTo(n,i,e,t)}function _t(){const i=this;if(i.destroyed)return;const{params:e,slidesEl:t}=i,s=e.slidesPerView==="auto"?i.slidesPerViewDynamic():e.slidesPerView;let r=i.getSlideIndexWhenGrid(i.clickedIndex),n;const o=i.isElement?"swiper-slide":`.${e.slideClass}`,a=i.grid&&i.params.grid&&i.params.grid.rows>1;if(e.loop){if(i.animating)return;n=parseInt(i.clickedSlide.getAttribute("data-swiper-slide-index"),10),e.centeredSlides?i.slideToLoop(n):r>(a?(i.slides.length-s)/2-(i.params.grid.rows-1):i.slides.length-s)?(i.loopFix(),r=i.getSlideIndex(H(t,`${o}[data-swiper-slide-index="${n}"]`)[0]),Se(()=>{i.slideTo(r)})):i.slideTo(r)}else i.slideTo(r)}var Dt={slideTo:Pt,slideToLoop:Mt,slideNext:Ct,slidePrev:Lt,slideReset:Ot,slideToClosest:At,slideToClickedSlide:_t};function zt(i,e){const t=this,{params:s,slidesEl:r}=t;if(!s.loop||t.virtual&&t.params.virtual.enabled)return;const n=()=>{H(r,`.${s.slideClass}, swiper-slide`).forEach((m,p)=>{m.setAttribute("data-swiper-slide-index",p)})},o=()=>{const f=H(r,`.${s.slideBlankClass}`);f.forEach(m=>{m.remove()}),f.length>0&&(t.recalcSlides(),t.updateSlides())},a=t.grid&&s.grid&&s.grid.rows>1;s.loopAddBlankSlides&&(s.slidesPerGroup>1||a)&&o();const d=s.slidesPerGroup*(a?s.grid.rows:1),l=t.slides.length%d!==0,c=a&&t.slides.length%s.grid.rows!==0,u=f=>{for(let m=0;m<f;m+=1){const p=t.isElement?ne("swiper-slide",[s.slideBlankClass]):ne("div",[s.slideClass,s.slideBlankClass]);t.slidesEl.append(p)}};if(l){if(s.loopAddBlankSlides){const f=d-t.slides.length%d;u(f),t.recalcSlides(),t.updateSlides()}else ee("Swiper Loop Warning: The number of slides is not even to slidesPerGroup, loop mode may not function properly. You need to add more slides (or make duplicates, or empty slides)");n()}else if(c){if(s.loopAddBlankSlides){const f=s.grid.rows-t.slides.length%s.grid.rows;u(f),t.recalcSlides(),t.updateSlides()}else ee("Swiper Loop Warning: The number of slides is not even to grid.rows, loop mode may not function properly. You need to add more slides (or make duplicates, or empty slides)");n()}else n();t.loopFix({slideRealIndex:i,direction:s.centeredSlides?void 0:"next",initial:e})}function kt(i){let{slideRealIndex:e,slideTo:t=!0,direction:s,setTranslate:r,activeSlideIndex:n,initial:o,byController:a,byMousewheel:d}=i===void 0?{}:i;const l=this;if(!l.params.loop)return;l.emit("beforeLoopFix");const{slides:c,allowSlidePrev:u,allowSlideNext:f,slidesEl:m,params:p}=l,{centeredSlides:w,initialSlide:I}=p;if(l.allowSlidePrev=!0,l.allowSlideNext=!0,l.virtual&&p.virtual.enabled){t&&(!p.centeredSlides&&l.snapIndex===0?l.slideTo(l.virtual.slides.length,0,!1,!0):p.centeredSlides&&l.snapIndex<p.slidesPerView?l.slideTo(l.virtual.slides.length+l.snapIndex,0,!1,!0):l.snapIndex===l.snapGrid.length-1&&l.slideTo(l.virtual.slidesBefore,0,!1,!0)),l.allowSlidePrev=u,l.allowSlideNext=f,l.emit("loopFix");return}let h=p.slidesPerView;h==="auto"?h=l.slidesPerViewDynamic():(h=Math.ceil(parseFloat(p.slidesPerView,10)),w&&h%2===0&&(h=h+1));const T=p.slidesPerGroupAuto?h:p.slidesPerGroup;let v=w?Math.max(T,Math.ceil(h/2)):T;v%T!==0&&(v+=T-v%T),v+=p.loopAdditionalSlides,l.loopedSlides=v;const E=l.grid&&p.grid&&p.grid.rows>1;c.length<h+v||l.params.effect==="cards"&&c.length<h+v*2?ee("Swiper Loop Warning: The number of slides is not enough for loop mode, it will be disabled or not function properly. You need to add more slides (or make duplicates) or lower the values of slidesPerView and slidesPerGroup parameters"):E&&p.grid.fill==="row"&&ee("Swiper Loop Warning: Loop mode is not compatible with grid.fill = `row`");const S=[],L=[],C=E?Math.ceil(c.length/p.grid.rows):c.length,x=o&&C-I<h&&!w;let O=x?I:l.activeIndex;typeof n>"u"?n=l.getSlideIndex(c.find(M=>M.classList.contains(p.slideActiveClass))):O=n;const g=s==="next"||!s,y=s==="prev"||!s;let P=0,A=0;const W=(E?c[n].column:n)+(w&&typeof r>"u"?-h/2+.5:0);if(W<v){P=Math.max(v-W,T);for(let M=0;M<v-W;M+=1){const D=M-Math.floor(M/C)*C;if(E){const b=C-D-1;for(let _=c.length-1;_>=0;_-=1)c[_].column===b&&S.push(_)}else S.push(C-D-1)}}else if(W+h>C-v){A=Math.max(W-(C-v*2),T),x&&(A=Math.max(A,h-C+I+1));for(let M=0;M<A;M+=1){const D=M-Math.floor(M/C)*C;E?c.forEach((b,_)=>{b.column===D&&L.push(_)}):L.push(D)}}if(l.__preventObserver__=!0,requestAnimationFrame(()=>{l.__preventObserver__=!1}),l.params.effect==="cards"&&c.length<h+v*2&&(L.includes(n)&&L.splice(L.indexOf(n),1),S.includes(n)&&S.splice(S.indexOf(n),1)),y&&S.forEach(M=>{c[M].swiperLoopMoveDOM=!0,m.prepend(c[M]),c[M].swiperLoopMoveDOM=!1}),g&&L.forEach(M=>{c[M].swiperLoopMoveDOM=!0,m.append(c[M]),c[M].swiperLoopMoveDOM=!1}),l.recalcSlides(),p.slidesPerView==="auto"?l.updateSlides():E&&(S.length>0&&y||L.length>0&&g)&&l.slides.forEach((M,D)=>{l.grid.updateSlide(D,M,l.slides)}),p.watchSlidesProgress&&l.updateSlidesOffset(),t){if(S.length>0&&y){if(typeof e>"u"){const M=l.slidesGrid[O],b=l.slidesGrid[O+P]-M;d?l.setTranslate(l.translate-b):(l.slideTo(O+Math.ceil(P),0,!1,!0),r&&(l.touchEventsData.startTranslate=l.touchEventsData.startTranslate-b,l.touchEventsData.currentTranslate=l.touchEventsData.currentTranslate-b))}else if(r){const M=E?S.length/p.grid.rows:S.length;l.slideTo(l.activeIndex+M,0,!1,!0),l.touchEventsData.currentTranslate=l.translate}}else if(L.length>0&&g)if(typeof e>"u"){const M=l.slidesGrid[O],b=l.slidesGrid[O-A]-M;d?l.setTranslate(l.translate-b):(l.slideTo(O-A,0,!1,!0),r&&(l.touchEventsData.startTranslate=l.touchEventsData.startTranslate-b,l.touchEventsData.currentTranslate=l.touchEventsData.currentTranslate-b))}else{const M=E?L.length/p.grid.rows:L.length;l.slideTo(l.activeIndex-M,0,!1,!0)}}if(l.allowSlidePrev=u,l.allowSlideNext=f,l.controller&&l.controller.control&&!a){const M={slideRealIndex:e,direction:s,setTranslate:r,activeSlideIndex:n,byController:!0};Array.isArray(l.controller.control)?l.controller.control.forEach(D=>{!D.destroyed&&D.params.loop&&D.loopFix({...M,slideTo:D.params.slidesPerView===p.slidesPerView?t:!1})}):l.controller.control instanceof l.constructor&&l.controller.control.params.loop&&l.controller.control.loopFix({...M,slideTo:l.controller.control.params.slidesPerView===p.slidesPerView?t:!1})}l.emit("loopFix")}function Gt(){const i=this,{params:e,slidesEl:t}=i;if(!e.loop||!t||i.virtual&&i.params.virtual.enabled)return;i.recalcSlides();const s=[];i.slides.forEach(r=>{const n=typeof r.swiperSlideIndex>"u"?r.getAttribute("data-swiper-slide-index")*1:r.swiperSlideIndex;s[n]=r}),i.slides.forEach(r=>{r.removeAttribute("data-swiper-slide-index")}),s.forEach(r=>{t.append(r)}),i.recalcSlides(),i.slideTo(i.realIndex,0)}var Nt={loopCreate:zt,loopFix:kt,loopDestroy:Gt};function Bt(i){const e=this;if(!e.params.simulateTouch||e.params.watchOverflow&&e.isLocked||e.params.cssMode)return;const t=e.params.touchEventsTarget==="container"?e.el:e.wrapperEl;e.isElement&&(e.__preventObserver__=!0),t.style.cursor="move",t.style.cursor=i?"grabbing":"grab",e.isElement&&requestAnimationFrame(()=>{e.__preventObserver__=!1})}function Rt(){const i=this;i.params.watchOverflow&&i.isLocked||i.params.cssMode||(i.isElement&&(i.__preventObserver__=!0),i[i.params.touchEventsTarget==="container"?"el":"wrapperEl"].style.cursor="",i.isElement&&requestAnimationFrame(()=>{i.__preventObserver__=!1}))}var Vt={setGrabCursor:Bt,unsetGrabCursor:Rt};function Ft(i,e){e===void 0&&(e=this);function t(s){if(!s||s===R()||s===z())return null;s.assignedSlot&&(s=s.assignedSlot);const r=s.closest(i);return!r&&!s.getRootNode?null:r||t(s.getRootNode().host)}return t(e)}function Le(i,e,t){const s=z(),{params:r}=i,n=r.edgeSwipeDetection,o=r.edgeSwipeThreshold;return n&&(t<=o||t>=s.innerWidth-o)?n==="prevent"?(e.preventDefault(),!0):!1:!0}function $t(i){const e=this,t=R();let s=i;s.originalEvent&&(s=s.originalEvent);const r=e.touchEventsData;if(s.type==="pointerdown"){if(r.pointerId!==null&&r.pointerId!==s.pointerId)return;r.pointerId=s.pointerId}else s.type==="touchstart"&&s.targetTouches.length===1&&(r.touchId=s.targetTouches[0].identifier);if(s.type==="touchstart"){Le(e,s,s.targetTouches[0].pageX);return}const{params:n,touches:o,enabled:a}=e;if(!a||!n.simulateTouch&&s.pointerType==="mouse"||e.animating&&n.preventInteractionOnTransition)return;!e.animating&&n.cssMode&&n.loop&&e.loopFix();let d=s.target;if(n.touchEventsTarget==="wrapper"&&!Ue(d,e.wrapperEl)||"which"in s&&s.which===3||"button"in s&&s.button>0||r.isTouched&&r.isMoved)return;const l=!!n.noSwipingClass&&n.noSwipingClass!=="",c=s.composedPath?s.composedPath():s.path;l&&s.target&&s.target.shadowRoot&&c&&(d=c[0]);const u=n.noSwipingSelector?n.noSwipingSelector:`.${n.noSwipingClass}`,f=!!(s.target&&s.target.shadowRoot);if(n.noSwiping&&(f?Ft(u,d):d.closest(u))){e.allowClick=!0;return}if(n.swipeHandler&&!d.closest(n.swipeHandler))return;o.currentX=s.pageX,o.currentY=s.pageY;const m=o.currentX,p=o.currentY;if(!Le(e,s,m))return;Object.assign(r,{isTouched:!0,isMoved:!1,allowTouchCallbacks:!0,isScrolling:void 0,startMoving:void 0}),o.startX=m,o.startY=p,r.touchStartTime=Q(),e.allowClick=!0,e.updateSize(),e.swipeDirection=void 0,n.threshold>0&&(r.allowThresholdMove=!1);let w=!0;d.matches(r.focusableElements)&&(w=!1,d.nodeName==="SELECT"&&(r.isTouched=!1)),t.activeElement&&t.activeElement.matches(r.focusableElements)&&t.activeElement!==d&&(s.pointerType==="mouse"||s.pointerType!=="mouse"&&!d.matches(r.focusableElements))&&t.activeElement.blur();const I=w&&e.allowTouchMove&&n.touchStartPreventDefault;(n.touchStartForcePreventDefault||I)&&!d.isContentEditable&&s.preventDefault(),n.freeMode&&n.freeMode.enabled&&e.freeMode&&e.animating&&!n.cssMode&&e.freeMode.onTouchStart(),e.emit("touchStart",s)}function Wt(i){const e=R(),t=this,s=t.touchEventsData,{params:r,touches:n,rtlTranslate:o,enabled:a}=t;if(!a||!r.simulateTouch&&i.pointerType==="mouse")return;let d=i;if(d.originalEvent&&(d=d.originalEvent),d.type==="pointermove"&&(s.touchId!==null||d.pointerId!==s.pointerId))return;let l;if(d.type==="touchmove"){if(l=[...d.changedTouches].find(S=>S.identifier===s.touchId),!l||l.identifier!==s.touchId)return}else l=d;if(!s.isTouched){s.startMoving&&s.isScrolling&&t.emit("touchMoveOpposite",d);return}const c=l.pageX,u=l.pageY;if(d.preventedByNestedSwiper){n.startX=c,n.startY=u;return}if(!t.allowTouchMove){d.target.matches(s.focusableElements)||(t.allowClick=!1),s.isTouched&&(Object.assign(n,{startX:c,startY:u,currentX:c,currentY:u}),s.touchStartTime=Q());return}if(r.touchReleaseOnEdges&&!r.loop)if(t.isVertical()){if(u<n.startY&&t.translate<=t.maxTranslate()||u>n.startY&&t.translate>=t.minTranslate()){s.isTouched=!1,s.isMoved=!1;return}}else{if(o&&(c>n.startX&&-t.translate<=t.maxTranslate()||c<n.startX&&-t.translate>=t.minTranslate()))return;if(!o&&(c<n.startX&&t.translate<=t.maxTranslate()||c>n.startX&&t.translate>=t.minTranslate()))return}if(e.activeElement&&e.activeElement.matches(s.focusableElements)&&e.activeElement!==d.target&&d.pointerType!=="mouse"&&e.activeElement.blur(),e.activeElement&&d.target===e.activeElement&&d.target.matches(s.focusableElements)){s.isMoved=!0,t.allowClick=!1;return}s.allowTouchCallbacks&&t.emit("touchMove",d),n.previousX=n.currentX,n.previousY=n.currentY,n.currentX=c,n.currentY=u;const f=n.currentX-n.startX,m=n.currentY-n.startY;if(t.params.threshold&&Math.sqrt(f**2+m**2)<t.params.threshold)return;if(typeof s.isScrolling>"u"){let S;t.isHorizontal()&&n.currentY===n.startY||t.isVertical()&&n.currentX===n.startX?s.isScrolling=!1:f*f+m*m>=25&&(S=Math.atan2(Math.abs(m),Math.abs(f))*180/Math.PI,s.isScrolling=t.isHorizontal()?S>r.touchAngle:90-S>r.touchAngle)}if(s.isScrolling&&t.emit("touchMoveOpposite",d),typeof s.startMoving>"u"&&(n.currentX!==n.startX||n.currentY!==n.startY)&&(s.startMoving=!0),s.isScrolling||d.type==="touchmove"&&s.preventTouchMoveFromPointerMove){s.isTouched=!1;return}if(!s.startMoving)return;t.allowClick=!1,!r.cssMode&&d.cancelable&&d.preventDefault(),r.touchMoveStopPropagation&&!r.nested&&d.stopPropagation();let p=t.isHorizontal()?f:m,w=t.isHorizontal()?n.currentX-n.previousX:n.currentY-n.previousY;r.oneWayMovement&&(p=Math.abs(p)*(o?1:-1),w=Math.abs(w)*(o?1:-1)),n.diff=p,p*=r.touchRatio,o&&(p=-p,w=-w);const I=t.touchesDirection;t.swipeDirection=p>0?"prev":"next",t.touchesDirection=w>0?"prev":"next";const h=t.params.loop&&!r.cssMode,T=t.touchesDirection==="next"&&t.allowSlideNext||t.touchesDirection==="prev"&&t.allowSlidePrev;if(!s.isMoved){if(h&&T&&t.loopFix({direction:t.swipeDirection}),s.startTranslate=t.getTranslate(),t.setTransition(0),t.animating){const S=new window.CustomEvent("transitionend",{bubbles:!0,cancelable:!0,detail:{bySwiperTouchMove:!0}});t.wrapperEl.dispatchEvent(S)}s.allowMomentumBounce=!1,r.grabCursor&&(t.allowSlideNext===!0||t.allowSlidePrev===!0)&&t.setGrabCursor(!0),t.emit("sliderFirstMove",d)}if(new Date().getTime(),r._loopSwapReset!==!1&&s.isMoved&&s.allowThresholdMove&&I!==t.touchesDirection&&h&&T&&Math.abs(p)>=1){Object.assign(n,{startX:c,startY:u,currentX:c,currentY:u,startTranslate:s.currentTranslate}),s.loopSwapReset=!0,s.startTranslate=s.currentTranslate;return}t.emit("sliderMove",d),s.isMoved=!0,s.currentTranslate=p+s.startTranslate;let v=!0,E=r.resistanceRatio;if(r.touchReleaseOnEdges&&(E=0),p>0?(h&&T&&s.allowThresholdMove&&s.currentTranslate>(r.centeredSlides?t.minTranslate()-t.slidesSizesGrid[t.activeIndex+1]-(r.slidesPerView!=="auto"&&t.slides.length-r.slidesPerView>=2?t.slidesSizesGrid[t.activeIndex+1]+t.params.spaceBetween:0)-t.params.spaceBetween:t.minTranslate())&&t.loopFix({direction:"prev",setTranslate:!0,activeSlideIndex:0}),s.currentTranslate>t.minTranslate()&&(v=!1,r.resistance&&(s.currentTranslate=t.minTranslate()-1+(-t.minTranslate()+s.startTranslate+p)**E))):p<0&&(h&&T&&s.allowThresholdMove&&s.currentTranslate<(r.centeredSlides?t.maxTranslate()+t.slidesSizesGrid[t.slidesSizesGrid.length-1]+t.params.spaceBetween+(r.slidesPerView!=="auto"&&t.slides.length-r.slidesPerView>=2?t.slidesSizesGrid[t.slidesSizesGrid.length-1]+t.params.spaceBetween:0):t.maxTranslate())&&t.loopFix({direction:"next",setTranslate:!0,activeSlideIndex:t.slides.length-(r.slidesPerView==="auto"?t.slidesPerViewDynamic():Math.ceil(parseFloat(r.slidesPerView,10)))}),s.currentTranslate<t.maxTranslate()&&(v=!1,r.resistance&&(s.currentTranslate=t.maxTranslate()+1-(t.maxTranslate()-s.startTranslate-p)**E))),v&&(d.preventedByNestedSwiper=!0),!t.allowSlideNext&&t.swipeDirection==="next"&&s.currentTranslate<s.startTranslate&&(s.currentTranslate=s.startTranslate),!t.allowSlidePrev&&t.swipeDirection==="prev"&&s.currentTranslate>s.startTranslate&&(s.currentTranslate=s.startTranslate),!t.allowSlidePrev&&!t.allowSlideNext&&(s.currentTranslate=s.startTranslate),r.threshold>0)if(Math.abs(p)>r.threshold||s.allowThresholdMove){if(!s.allowThresholdMove){s.allowThresholdMove=!0,n.startX=n.currentX,n.startY=n.currentY,s.currentTranslate=s.startTranslate,n.diff=t.isHorizontal()?n.currentX-n.startX:n.currentY-n.startY;return}}else{s.currentTranslate=s.startTranslate;return}!r.followFinger||r.cssMode||((r.freeMode&&r.freeMode.enabled&&t.freeMode||r.watchSlidesProgress)&&(t.updateActiveIndex(),t.updateSlidesClasses()),r.freeMode&&r.freeMode.enabled&&t.freeMode&&t.freeMode.onTouchMove(),t.updateProgress(s.currentTranslate),t.setTranslate(s.currentTranslate))}function Ht(i){const e=this,t=e.touchEventsData;let s=i;s.originalEvent&&(s=s.originalEvent);let r;if(s.type==="touchend"||s.type==="touchcancel"){if(r=[...s.changedTouches].find(S=>S.identifier===t.touchId),!r||r.identifier!==t.touchId)return}else{if(t.touchId!==null||s.pointerId!==t.pointerId)return;r=s}if(["pointercancel","pointerout","pointerleave","contextmenu"].includes(s.type)&&!(["pointercancel","contextmenu"].includes(s.type)&&(e.browser.isSafari||e.browser.isWebView)))return;t.pointerId=null,t.touchId=null;const{params:o,touches:a,rtlTranslate:d,slidesGrid:l,enabled:c}=e;if(!c||!o.simulateTouch&&s.pointerType==="mouse")return;if(t.allowTouchCallbacks&&e.emit("touchEnd",s),t.allowTouchCallbacks=!1,!t.isTouched){t.isMoved&&o.grabCursor&&e.setGrabCursor(!1),t.isMoved=!1,t.startMoving=!1;return}o.grabCursor&&t.isMoved&&t.isTouched&&(e.allowSlideNext===!0||e.allowSlidePrev===!0)&&e.setGrabCursor(!1);const u=Q(),f=u-t.touchStartTime;if(e.allowClick){const S=s.path||s.composedPath&&s.composedPath();e.updateClickedSlide(S&&S[0]||s.target,S),e.emit("tap click",s),f<300&&u-t.lastClickTime<300&&e.emit("doubleTap doubleClick",s)}if(t.lastClickTime=Q(),Se(()=>{e.destroyed||(e.allowClick=!0)}),!t.isTouched||!t.isMoved||!e.swipeDirection||a.diff===0&&!t.loopSwapReset||t.currentTranslate===t.startTranslate&&!t.loopSwapReset){t.isTouched=!1,t.isMoved=!1,t.startMoving=!1;return}t.isTouched=!1,t.isMoved=!1,t.startMoving=!1;let m;if(o.followFinger?m=d?e.translate:-e.translate:m=-t.currentTranslate,o.cssMode)return;if(o.freeMode&&o.freeMode.enabled){e.freeMode.onTouchEnd({currentPos:m});return}const p=m>=-e.maxTranslate()&&!e.params.loop;let w=0,I=e.slidesSizesGrid[0];for(let S=0;S<l.length;S+=S<o.slidesPerGroupSkip?1:o.slidesPerGroup){const L=S<o.slidesPerGroupSkip-1?1:o.slidesPerGroup;typeof l[S+L]<"u"?(p||m>=l[S]&&m<l[S+L])&&(w=S,I=l[S+L]-l[S]):(p||m>=l[S])&&(w=S,I=l[l.length-1]-l[l.length-2])}let h=null,T=null;o.rewind&&(e.isBeginning?T=o.virtual&&o.virtual.enabled&&e.virtual?e.virtual.slides.length-1:e.slides.length-1:e.isEnd&&(h=0));const v=(m-l[w])/I,E=w<o.slidesPerGroupSkip-1?1:o.slidesPerGroup;if(f>o.longSwipesMs){if(!o.longSwipes){e.slideTo(e.activeIndex);return}e.swipeDirection==="next"&&(v>=o.longSwipesRatio?e.slideTo(o.rewind&&e.isEnd?h:w+E):e.slideTo(w)),e.swipeDirection==="prev"&&(v>1-o.longSwipesRatio?e.slideTo(w+E):T!==null&&v<0&&Math.abs(v)>o.longSwipesRatio?e.slideTo(T):e.slideTo(w))}else{if(!o.shortSwipes){e.slideTo(e.activeIndex);return}e.navigation&&(s.target===e.navigation.nextEl||s.target===e.navigation.prevEl)?s.target===e.navigation.nextEl?e.slideTo(w+E):e.slideTo(w):(e.swipeDirection==="next"&&e.slideTo(h!==null?h:w+E),e.swipeDirection==="prev"&&e.slideTo(T!==null?T:w))}}function Oe(){const i=this,{params:e,el:t}=i;if(t&&t.offsetWidth===0)return;e.breakpoints&&i.setBreakpoint();const{allowSlideNext:s,allowSlidePrev:r,snapGrid:n}=i,o=i.virtual&&i.params.virtual.enabled;i.allowSlideNext=!0,i.allowSlidePrev=!0,i.updateSize(),i.updateSlides(),i.updateSlidesClasses();const a=o&&e.loop;(e.slidesPerView==="auto"||e.slidesPerView>1)&&i.isEnd&&!i.isBeginning&&!i.params.centeredSlides&&!a?i.slideTo(i.slides.length-1,0,!1,!0):i.params.loop&&!o?i.slideToLoop(i.realIndex,0,!1,!0):i.slideTo(i.activeIndex,0,!1,!0),i.autoplay&&i.autoplay.running&&i.autoplay.paused&&(clearTimeout(i.autoplay.resizeTimeout),i.autoplay.resizeTimeout=setTimeout(()=>{i.autoplay&&i.autoplay.running&&i.autoplay.paused&&i.autoplay.resume()},500)),i.allowSlidePrev=r,i.allowSlideNext=s,i.params.watchOverflow&&n!==i.snapGrid&&i.checkOverflow()}function Yt(i){const e=this;e.enabled&&(e.allowClick||(e.params.preventClicks&&i.preventDefault(),e.params.preventClicksPropagation&&e.animating&&(i.stopPropagation(),i.stopImmediatePropagation())))}function Xt(){const i=this,{wrapperEl:e,rtlTranslate:t,enabled:s}=i;if(!s)return;i.previousTranslate=i.translate,i.isHorizontal()?i.translate=-e.scrollLeft:i.translate=-e.scrollTop,i.translate===0&&(i.translate=0),i.updateActiveIndex(),i.updateSlidesClasses();let r;const n=i.maxTranslate()-i.minTranslate();n===0?r=0:r=(i.translate-i.minTranslate())/n,r!==i.progress&&i.updateProgress(t?-i.translate:i.translate),i.emit("setTranslate",i.translate,!1)}function jt(i){const e=this;te(e,i.target),!(e.params.cssMode||e.params.slidesPerView!=="auto"&&!e.params.autoHeight)&&e.update()}function qt(){const i=this;i.documentTouchHandlerProceeded||(i.documentTouchHandlerProceeded=!0,i.params.touchReleaseOnEdges&&(i.el.style.touchAction="auto"))}const Ae=(i,e)=>{const t=R(),{params:s,el:r,wrapperEl:n,device:o}=i,a=!!s.nested,d=e==="on"?"addEventListener":"removeEventListener",l=e;!r||typeof r=="string"||(t[d]("touchstart",i.onDocumentTouchStart,{passive:!1,capture:a}),r[d]("touchstart",i.onTouchStart,{passive:!1}),r[d]("pointerdown",i.onTouchStart,{passive:!1}),t[d]("touchmove",i.onTouchMove,{passive:!1,capture:a}),t[d]("pointermove",i.onTouchMove,{passive:!1,capture:a}),t[d]("touchend",i.onTouchEnd,{passive:!0}),t[d]("pointerup",i.onTouchEnd,{passive:!0}),t[d]("pointercancel",i.onTouchEnd,{passive:!0}),t[d]("touchcancel",i.onTouchEnd,{passive:!0}),t[d]("pointerout",i.onTouchEnd,{passive:!0}),t[d]("pointerleave",i.onTouchEnd,{passive:!0}),t[d]("contextmenu",i.onTouchEnd,{passive:!0}),(s.preventClicks||s.preventClicksPropagation)&&r[d]("click",i.onClick,!0),s.cssMode&&n[d]("scroll",i.onScroll),s.updateOnWindowResize?i[l](o.ios||o.android?"resize orientationchange observerUpdate":"resize observerUpdate",Oe,!0):i[l]("observerUpdate",Oe,!0),r[d]("load",i.onLoad,{capture:!0}))};function Ut(){const i=this,{params:e}=i;i.onTouchStart=$t.bind(i),i.onTouchMove=Wt.bind(i),i.onTouchEnd=Ht.bind(i),i.onDocumentTouchStart=qt.bind(i),e.cssMode&&(i.onScroll=Xt.bind(i)),i.onClick=Yt.bind(i),i.onLoad=jt.bind(i),Ae(i,"on")}function Kt(){Ae(this,"off")}var Qt={attachEvents:Ut,detachEvents:Kt};const _e=(i,e)=>i.grid&&e.grid&&e.grid.rows>1;function Jt(){const i=this,{realIndex:e,initialized:t,params:s,el:r}=i,n=s.breakpoints;if(!n||n&&Object.keys(n).length===0)return;const o=R(),a=s.breakpointsBase==="window"||!s.breakpointsBase?s.breakpointsBase:"container",d=["window","container"].includes(s.breakpointsBase)||!s.breakpointsBase?i.el:o.querySelector(s.breakpointsBase),l=i.getBreakpoint(n,a,d);if(!l||i.currentBreakpoint===l)return;const u=(l in n?n[l]:void 0)||i.originalParams,f=_e(i,s),m=_e(i,u),p=i.params.grabCursor,w=u.grabCursor,I=s.enabled;f&&!m?(r.classList.remove(`${s.containerModifierClass}grid`,`${s.containerModifierClass}grid-column`),i.emitContainerClasses()):!f&&m&&(r.classList.add(`${s.containerModifierClass}grid`),(u.grid.fill&&u.grid.fill==="column"||!u.grid.fill&&s.grid.fill==="column")&&r.classList.add(`${s.containerModifierClass}grid-column`),i.emitContainerClasses()),p&&!w?i.unsetGrabCursor():!p&&w&&i.setGrabCursor(),["navigation","pagination","scrollbar"].forEach(L=>{if(typeof u[L]>"u")return;const C=s[L]&&s[L].enabled,x=u[L]&&u[L].enabled;C&&!x&&i[L].disable(),!C&&x&&i[L].enable()});const h=u.direction&&u.direction!==s.direction,T=s.loop&&(u.slidesPerView!==s.slidesPerView||h),v=s.loop;h&&t&&i.changeDirection(),G(i.params,u);const E=i.params.enabled,S=i.params.loop;Object.assign(i,{allowTouchMove:i.params.allowTouchMove,allowSlideNext:i.params.allowSlideNext,allowSlidePrev:i.params.allowSlidePrev}),I&&!E?i.disable():!I&&E&&i.enable(),i.currentBreakpoint=l,i.emit("_beforeBreakpoint",u),t&&(T?(i.loopDestroy(),i.loopCreate(e),i.updateSlides()):!v&&S?(i.loopCreate(e),i.updateSlides()):v&&!S&&i.loopDestroy()),i.emit("breakpoint",u)}function Zt(i,e,t){if(e===void 0&&(e="window"),!i||e==="container"&&!t)return;let s=!1;const r=z(),n=e==="window"?r.innerHeight:t.clientHeight,o=Object.keys(i).map(a=>{if(typeof a=="string"&&a.indexOf("@")===0){const d=parseFloat(a.substr(1));return{value:n*d,point:a}}return{value:a,point:a}});o.sort((a,d)=>parseInt(a.value,10)-parseInt(d.value,10));for(let a=0;a<o.length;a+=1){const{point:d,value:l}=o[a];e==="window"?r.matchMedia(`(min-width: ${l}px)`).matches&&(s=d):l<=t.clientWidth&&(s=d)}return s||"max"}var ei={setBreakpoint:Jt,getBreakpoint:Zt};function ti(i,e){const t=[];return i.forEach(s=>{typeof s=="object"?Object.keys(s).forEach(r=>{s[r]&&t.push(e+r)}):typeof s=="string"&&t.push(e+s)}),t}function ii(){const i=this,{classNames:e,params:t,rtl:s,el:r,device:n}=i,o=ti(["initialized",t.direction,{"free-mode":i.params.freeMode&&t.freeMode.enabled},{autoheight:t.autoHeight},{rtl:s},{grid:t.grid&&t.grid.rows>1},{"grid-column":t.grid&&t.grid.rows>1&&t.grid.fill==="column"},{android:n.android},{ios:n.ios},{"css-mode":t.cssMode},{centered:t.cssMode&&t.centeredSlides},{"watch-progress":t.watchSlidesProgress}],t.containerModifierClass);e.push(...o),r.classList.add(...e),i.emitContainerClasses()}function si(){const i=this,{el:e,classNames:t}=i;!e||typeof e=="string"||(e.classList.remove(...t),i.emitContainerClasses())}var ri={addClasses:ii,removeClasses:si};function ni(){const i=this,{isLocked:e,params:t}=i,{slidesOffsetBefore:s}=t;if(s){const r=i.slides.length-1,n=i.slidesGrid[r]+i.slidesSizesGrid[r]+s*2;i.isLocked=i.size>n}else i.isLocked=i.snapGrid.length===1;t.allowSlideNext===!0&&(i.allowSlideNext=!i.isLocked),t.allowSlidePrev===!0&&(i.allowSlidePrev=!i.isLocked),e&&e!==i.isLocked&&(i.isEnd=!1),e!==i.isLocked&&i.emit(i.isLocked?"lock":"unlock")}var ai={checkOverflow:ni},De={init:!0,direction:"horizontal",oneWayMovement:!1,swiperElementNodeName:"SWIPER-CONTAINER",touchEventsTarget:"wrapper",initialSlide:0,speed:300,cssMode:!1,updateOnWindowResize:!0,resizeObserver:!0,nested:!1,createElements:!1,eventsPrefix:"swiper",enabled:!0,focusableElements:"input, select, option, textarea, button, video, label",width:null,height:null,preventInteractionOnTransition:!1,userAgent:null,url:null,edgeSwipeDetection:!1,edgeSwipeThreshold:20,autoHeight:!1,setWrapperSize:!1,virtualTranslate:!1,effect:"slide",breakpoints:void 0,breakpointsBase:"window",spaceBetween:0,slidesPerView:1,slidesPerGroup:1,slidesPerGroupSkip:0,slidesPerGroupAuto:!1,centeredSlides:!1,centeredSlidesBounds:!1,slidesOffsetBefore:0,slidesOffsetAfter:0,normalizeSlideIndex:!0,centerInsufficientSlides:!1,watchOverflow:!0,roundLengths:!1,touchRatio:1,touchAngle:45,simulateTouch:!0,shortSwipes:!0,longSwipes:!0,longSwipesRatio:.5,longSwipesMs:300,followFinger:!0,allowTouchMove:!0,threshold:5,touchMoveStopPropagation:!1,touchStartPreventDefault:!0,touchStartForcePreventDefault:!1,touchReleaseOnEdges:!1,uniqueNavElements:!0,resistance:!0,resistanceRatio:.85,watchSlidesProgress:!1,grabCursor:!1,preventClicks:!0,preventClicksPropagation:!0,slideToClickedSlide:!1,loop:!1,loopAddBlankSlides:!0,loopAdditionalSlides:0,loopPreventsSliding:!0,rewind:!1,allowSlidePrev:!0,allowSlideNext:!0,swipeHandler:null,noSwiping:!0,noSwipingClass:"swiper-no-swiping",noSwipingSelector:null,passiveListeners:!0,maxBackfaceHiddenSlides:10,containerModifierClass:"swiper-",slideClass:"swiper-slide",slideBlankClass:"swiper-slide-blank",slideActiveClass:"swiper-slide-active",slideVisibleClass:"swiper-slide-visible",slideFullyVisibleClass:"swiper-slide-fully-visible",slideNextClass:"swiper-slide-next",slidePrevClass:"swiper-slide-prev",wrapperClass:"swiper-wrapper",lazyPreloaderClass:"swiper-lazy-preloader",lazyPreloadPrevNext:0,runCallbacksOnInit:!0,_emitClasses:!1};function oi(i,e){return function(s){s===void 0&&(s={});const r=Object.keys(s)[0],n=s[r];if(typeof n!="object"||n===null){G(e,s);return}if(i[r]===!0&&(i[r]={enabled:!0}),r==="navigation"&&i[r]&&i[r].enabled&&!i[r].prevEl&&!i[r].nextEl&&(i[r].auto=!0),["pagination","scrollbar"].indexOf(r)>=0&&i[r]&&i[r].enabled&&!i[r].el&&(i[r].auto=!0),!(r in i&&"enabled"in n)){G(e,s);return}typeof i[r]=="object"&&!("enabled"in i[r])&&(i[r].enabled=!0),i[r]||(i[r]={enabled:!1}),G(e,s)}}const fe={eventsEmitter:rt,update:ht,translate:yt,transition:It,slide:Dt,loop:Nt,grabCursor:Vt,events:Qt,breakpoints:ei,checkOverflow:ai,classes:ri},pe={};class N{constructor(){let e,t;for(var s=arguments.length,r=new Array(s),n=0;n<s;n++)r[n]=arguments[n];r.length===1&&r[0].constructor&&Object.prototype.toString.call(r[0]).slice(8,-1)==="Object"?t=r[0]:[e,t]=r,t||(t={}),t=G({},t),e&&!t.el&&(t.el=e);const o=R();if(t.el&&typeof t.el=="string"&&o.querySelectorAll(t.el).length>1){const c=[];return o.querySelectorAll(t.el).forEach(u=>{const f=G({},t,{el:u});c.push(new N(f))}),c}const a=this;a.__swiper__=!0,a.support=xe(),a.device=Ie({userAgent:t.userAgent}),a.browser=Pe(),a.eventsListeners={},a.eventsAnyListeners=[],a.modules=[...a.__modules__],t.modules&&Array.isArray(t.modules)&&a.modules.push(...t.modules);const d={};a.modules.forEach(c=>{c({params:t,swiper:a,extendParams:oi(t,d),on:a.on.bind(a),once:a.once.bind(a),off:a.off.bind(a),emit:a.emit.bind(a)})});const l=G({},De,d);return a.params=G({},l,pe,t),a.originalParams=G({},a.params),a.passedParams=G({},t),a.params&&a.params.on&&Object.keys(a.params.on).forEach(c=>{a.on(c,a.params.on[c])}),a.params&&a.params.onAny&&a.onAny(a.params.onAny),Object.assign(a,{enabled:a.params.enabled,el:e,classNames:[],slides:[],slidesGrid:[],snapGrid:[],slidesSizesGrid:[],isHorizontal(){return a.params.direction==="horizontal"},isVertical(){return a.params.direction==="vertical"},activeIndex:0,realIndex:0,isBeginning:!0,isEnd:!1,translate:0,previousTranslate:0,progress:0,velocity:0,animating:!1,cssOverflowAdjustment(){return Math.trunc(this.translate/2**23)*2**23},allowSlideNext:a.params.allowSlideNext,allowSlidePrev:a.params.allowSlidePrev,touchEventsData:{isTouched:void 0,isMoved:void 0,allowTouchCallbacks:void 0,touchStartTime:void 0,isScrolling:void 0,currentTranslate:void 0,startTranslate:void 0,allowThresholdMove:void 0,focusableElements:a.params.focusableElements,lastClickTime:0,clickTimeout:void 0,velocities:[],allowMomentumBounce:void 0,startMoving:void 0,pointerId:null,touchId:null},allowClick:!0,allowTouchMove:a.params.allowTouchMove,touches:{startX:0,startY:0,currentX:0,currentY:0,diff:0},imagesToLoad:[],imagesLoaded:0}),a.emit("_swiper"),a.params.init&&a.init(),a}getDirectionLabel(e){return this.isHorizontal()?e:{width:"height","margin-top":"margin-left","margin-bottom ":"margin-right","margin-left":"margin-top","margin-right":"margin-bottom","padding-left":"padding-top","padding-right":"padding-bottom",marginRight:"marginBottom"}[e]}getSlideIndex(e){const{slidesEl:t,params:s}=this,r=H(t,`.${s.slideClass}, swiper-slide`),n=Ee(r[0]);return Ee(e)-n}getSlideIndexByData(e){return this.getSlideIndex(this.slides.find(t=>t.getAttribute("data-swiper-slide-index")*1===e))}getSlideIndexWhenGrid(e){return this.grid&&this.params.grid&&this.params.grid.rows>1&&(this.params.grid.fill==="column"?e=Math.floor(e/this.params.grid.rows):this.params.grid.fill==="row"&&(e=e%Math.ceil(this.slides.length/this.params.grid.rows))),e}recalcSlides(){const e=this,{slidesEl:t,params:s}=e;e.slides=H(t,`.${s.slideClass}, swiper-slide`)}enable(){const e=this;e.enabled||(e.enabled=!0,e.params.grabCursor&&e.setGrabCursor(),e.emit("enable"))}disable(){const e=this;e.enabled&&(e.enabled=!1,e.params.grabCursor&&e.unsetGrabCursor(),e.emit("disable"))}setProgress(e,t){const s=this;e=Math.min(Math.max(e,0),1);const r=s.minTranslate(),o=(s.maxTranslate()-r)*e+r;s.translateTo(o,typeof t>"u"?0:t),s.updateActiveIndex(),s.updateSlidesClasses()}emitContainerClasses(){const e=this;if(!e.params._emitClasses||!e.el)return;const t=e.el.className.split(" ").filter(s=>s.indexOf("swiper")===0||s.indexOf(e.params.containerModifierClass)===0);e.emit("_containerClasses",t.join(" "))}getSlideClasses(e){const t=this;return t.destroyed?"":e.className.split(" ").filter(s=>s.indexOf("swiper-slide")===0||s.indexOf(t.params.slideClass)===0).join(" ")}emitSlidesClasses(){const e=this;if(!e.params._emitClasses||!e.el)return;const t=[];e.slides.forEach(s=>{const r=e.getSlideClasses(s);t.push({slideEl:s,classNames:r}),e.emit("_slideClass",s,r)}),e.emit("_slideClasses",t)}slidesPerViewDynamic(e,t){e===void 0&&(e="current"),t===void 0&&(t=!1);const s=this,{params:r,slides:n,slidesGrid:o,slidesSizesGrid:a,size:d,activeIndex:l}=s;let c=1;if(typeof r.slidesPerView=="number")return r.slidesPerView;if(r.centeredSlides){let u=n[l]?Math.ceil(n[l].swiperSlideSize):0,f;for(let m=l+1;m<n.length;m+=1)n[m]&&!f&&(u+=Math.ceil(n[m].swiperSlideSize),c+=1,u>d&&(f=!0));for(let m=l-1;m>=0;m-=1)n[m]&&!f&&(u+=n[m].swiperSlideSize,c+=1,u>d&&(f=!0))}else if(e==="current")for(let u=l+1;u<n.length;u+=1)(t?o[u]+a[u]-o[l]<d:o[u]-o[l]<d)&&(c+=1);else for(let u=l-1;u>=0;u-=1)o[l]-o[u]<d&&(c+=1);return c}update(){const e=this;if(!e||e.destroyed)return;const{snapGrid:t,params:s}=e;s.breakpoints&&e.setBreakpoint(),[...e.el.querySelectorAll('[loading="lazy"]')].forEach(o=>{o.complete&&te(e,o)}),e.updateSize(),e.updateSlides(),e.updateProgress(),e.updateSlidesClasses();function r(){const o=e.rtlTranslate?e.translate*-1:e.translate,a=Math.min(Math.max(o,e.maxTranslate()),e.minTranslate());e.setTranslate(a),e.updateActiveIndex(),e.updateSlidesClasses()}let n;if(s.freeMode&&s.freeMode.enabled&&!s.cssMode)r(),s.autoHeight&&e.updateAutoHeight();else{if((s.slidesPerView==="auto"||s.slidesPerView>1)&&e.isEnd&&!s.centeredSlides){const o=e.virtual&&s.virtual.enabled?e.virtual.slides:e.slides;n=e.slideTo(o.length-1,0,!1,!0)}else n=e.slideTo(e.activeIndex,0,!1,!0);n||r()}s.watchOverflow&&t!==e.snapGrid&&e.checkOverflow(),e.emit("update")}changeDirection(e,t){t===void 0&&(t=!0);const s=this,r=s.params.direction;return e||(e=r==="horizontal"?"vertical":"horizontal"),e===r||e!=="horizontal"&&e!=="vertical"||(s.el.classList.remove(`${s.params.containerModifierClass}${r}`),s.el.classList.add(`${s.params.containerModifierClass}${e}`),s.emitContainerClasses(),s.params.direction=e,s.slides.forEach(n=>{e==="vertical"?n.style.width="":n.style.height=""}),s.emit("changeDirection"),t&&s.update()),s}changeLanguageDirection(e){const t=this;t.rtl&&e==="rtl"||!t.rtl&&e==="ltr"||(t.rtl=e==="rtl",t.rtlTranslate=t.params.direction==="horizontal"&&t.rtl,t.rtl?(t.el.classList.add(`${t.params.containerModifierClass}rtl`),t.el.dir="rtl"):(t.el.classList.remove(`${t.params.containerModifierClass}rtl`),t.el.dir="ltr"),t.update())}mount(e){const t=this;if(t.mounted)return!0;let s=e||t.params.el;if(typeof s=="string"&&(s=document.querySelector(s)),!s)return!1;s.swiper=t,s.parentNode&&s.parentNode.host&&s.parentNode.host.nodeName===t.params.swiperElementNodeName.toUpperCase()&&(t.isElement=!0);const r=()=>`.${(t.params.wrapperClass||"").trim().split(" ").join(".")}`;let o=s&&s.shadowRoot&&s.shadowRoot.querySelector?s.shadowRoot.querySelector(r()):H(s,r())[0];return!o&&t.params.createElements&&(o=ne("div",t.params.wrapperClass),s.append(o),H(s,`.${t.params.slideClass}`).forEach(a=>{o.append(a)})),Object.assign(t,{el:s,wrapperEl:o,slidesEl:t.isElement&&!s.parentNode.host.slideSlots?s.parentNode.host:o,hostEl:t.isElement?s.parentNode.host:s,mounted:!0,rtl:s.dir.toLowerCase()==="rtl"||Y(s,"direction")==="rtl",rtlTranslate:t.params.direction==="horizontal"&&(s.dir.toLowerCase()==="rtl"||Y(s,"direction")==="rtl"),wrongRTL:Y(o,"display")==="-webkit-box"}),!0}init(e){const t=this;if(t.initialized||t.mount(e)===!1)return t;t.emit("beforeInit"),t.params.breakpoints&&t.setBreakpoint(),t.addClasses(),t.updateSize(),t.updateSlides(),t.params.watchOverflow&&t.checkOverflow(),t.params.grabCursor&&t.enabled&&t.setGrabCursor(),t.params.loop&&t.virtual&&t.params.virtual.enabled?t.slideTo(t.params.initialSlide+t.virtual.slidesBefore,0,t.params.runCallbacksOnInit,!1,!0):t.slideTo(t.params.initialSlide,0,t.params.runCallbacksOnInit,!1,!0),t.params.loop&&t.loopCreate(void 0,!0),t.attachEvents();const r=[...t.el.querySelectorAll('[loading="lazy"]')];return t.isElement&&r.push(...t.hostEl.querySelectorAll('[loading="lazy"]')),r.forEach(n=>{n.complete?te(t,n):n.addEventListener("load",o=>{te(t,o.target)})}),ue(t),t.initialized=!0,ue(t),t.emit("init"),t.emit("afterInit"),t}destroy(e,t){e===void 0&&(e=!0),t===void 0&&(t=!0);const s=this,{params:r,el:n,wrapperEl:o,slides:a}=s;return typeof s.params>"u"||s.destroyed||(s.emit("beforeDestroy"),s.initialized=!1,s.detachEvents(),r.loop&&s.loopDestroy(),t&&(s.removeClasses(),n&&typeof n!="string"&&n.removeAttribute("style"),o&&o.removeAttribute("style"),a&&a.length&&a.forEach(d=>{d.classList.remove(r.slideVisibleClass,r.slideFullyVisibleClass,r.slideActiveClass,r.slideNextClass,r.slidePrevClass),d.removeAttribute("style"),d.removeAttribute("data-swiper-slide-index")})),s.emit("destroy"),Object.keys(s.eventsListeners).forEach(d=>{s.off(d)}),e!==!1&&(s.el&&typeof s.el!="string"&&(s.el.swiper=null),He(s)),s.destroyed=!0),null}static extendDefaults(e){G(pe,e)}static get extendedDefaults(){return pe}static get defaults(){return De}static installModule(e){N.prototype.__modules__||(N.prototype.__modules__=[]);const t=N.prototype.__modules__;typeof e=="function"&&t.indexOf(e)<0&&t.push(e)}static use(e){return Array.isArray(e)?(e.forEach(t=>N.installModule(t)),N):(N.installModule(e),N)}}Object.keys(fe).forEach(i=>{Object.keys(fe[i]).forEach(e=>{N.prototype[e]=fe[i][e]})}),N.use([it,st]);function li(i){let{swiper:e,extendParams:t,on:s,emit:r,params:n}=i;e.autoplay={running:!1,paused:!1,timeLeft:0},t({autoplay:{enabled:!1,delay:3e3,waitForTransition:!0,disableOnInteraction:!1,stopOnLastSlide:!1,reverseDirection:!1,pauseOnMouseEnter:!1}});let o,a,d=n&&n.autoplay?n.autoplay.delay:3e3,l=n&&n.autoplay?n.autoplay.delay:3e3,c,u=new Date().getTime(),f,m,p,w,I,h,T;function v(b){!e||e.destroyed||!e.wrapperEl||b.target===e.wrapperEl&&(e.wrapperEl.removeEventListener("transitionend",v),!(T||b.detail&&b.detail.bySwiperTouchMove)&&g())}const E=()=>{if(e.destroyed||!e.autoplay.running)return;e.autoplay.paused?f=!0:f&&(l=c,f=!1);const b=e.autoplay.paused?c:u+l-new Date().getTime();e.autoplay.timeLeft=b,r("autoplayTimeLeft",b,b/d),a=requestAnimationFrame(()=>{E()})},S=()=>{let b;return e.virtual&&e.params.virtual.enabled?b=e.slides.find(k=>k.classList.contains("swiper-slide-active")):b=e.slides[e.activeIndex],b?parseInt(b.getAttribute("data-swiper-autoplay"),10):void 0},L=b=>{if(e.destroyed||!e.autoplay.running)return;cancelAnimationFrame(a),E();let _=typeof b>"u"?e.params.autoplay.delay:b;d=e.params.autoplay.delay,l=e.params.autoplay.delay;const k=S();!Number.isNaN(k)&&k>0&&typeof b>"u"&&(_=k,d=k,l=k),c=_;const j=e.params.speed,se=()=>{!e||e.destroyed||(e.params.autoplay.reverseDirection?!e.isBeginning||e.params.loop||e.params.rewind?(e.slidePrev(j,!0,!0),r("autoplay")):e.params.autoplay.stopOnLastSlide||(e.slideTo(e.slides.length-1,j,!0,!0),r("autoplay")):!e.isEnd||e.params.loop||e.params.rewind?(e.slideNext(j,!0,!0),r("autoplay")):e.params.autoplay.stopOnLastSlide||(e.slideTo(0,j,!0,!0),r("autoplay")),e.params.cssMode&&(u=new Date().getTime(),requestAnimationFrame(()=>{L()})))};return _>0?(clearTimeout(o),o=setTimeout(()=>{se()},_)):requestAnimationFrame(()=>{se()}),_},C=()=>{u=new Date().getTime(),e.autoplay.running=!0,L(),r("autoplayStart")},x=()=>{e.autoplay.running=!1,clearTimeout(o),cancelAnimationFrame(a),r("autoplayStop")},O=(b,_)=>{if(e.destroyed||!e.autoplay.running)return;clearTimeout(o),b||(h=!0);const k=()=>{r("autoplayPause"),e.params.autoplay.waitForTransition?e.wrapperEl.addEventListener("transitionend",v):g()};if(e.autoplay.paused=!0,_){I&&(c=e.params.autoplay.delay),I=!1,k();return}c=(c||e.params.autoplay.delay)-(new Date().getTime()-u),!(e.isEnd&&c<0&&!e.params.loop)&&(c<0&&(c=0),k())},g=()=>{e.isEnd&&c<0&&!e.params.loop||e.destroyed||!e.autoplay.running||(u=new Date().getTime(),h?(h=!1,L(c)):L(),e.autoplay.paused=!1,r("autoplayResume"))},y=()=>{if(e.destroyed||!e.autoplay.running)return;const b=R();b.visibilityState==="hidden"&&(h=!0,O(!0)),b.visibilityState==="visible"&&g()},P=b=>{b.pointerType==="mouse"&&(h=!0,T=!0,!(e.animating||e.autoplay.paused)&&O(!0))},A=b=>{b.pointerType==="mouse"&&(T=!1,e.autoplay.paused&&g())},$=()=>{e.params.autoplay.pauseOnMouseEnter&&(e.el.addEventListener("pointerenter",P),e.el.addEventListener("pointerleave",A))},W=()=>{e.el&&typeof e.el!="string"&&(e.el.removeEventListener("pointerenter",P),e.el.removeEventListener("pointerleave",A))},M=()=>{R().addEventListener("visibilitychange",y)},D=()=>{R().removeEventListener("visibilitychange",y)};s("init",()=>{e.params.autoplay.enabled&&($(),M(),C())}),s("destroy",()=>{W(),D(),e.autoplay.running&&x()}),s("_freeModeStaticRelease",()=>{(p||h)&&g()}),s("_freeModeNoMomentumRelease",()=>{e.params.autoplay.disableOnInteraction?x():O(!0,!0)}),s("beforeTransitionStart",(b,_,k)=>{e.destroyed||!e.autoplay.running||(k||!e.params.autoplay.disableOnInteraction?O(!0,!0):x())}),s("sliderFirstMove",()=>{if(!(e.destroyed||!e.autoplay.running)){if(e.params.autoplay.disableOnInteraction){x();return}m=!0,p=!1,h=!1,w=setTimeout(()=>{h=!0,p=!0,O(!0)},200)}}),s("touchEnd",()=>{if(!(e.destroyed||!e.autoplay.running||!m)){if(clearTimeout(w),clearTimeout(o),e.params.autoplay.disableOnInteraction){p=!1,m=!1;return}p&&e.params.cssMode&&g(),p=!1,m=!1}}),s("slideChange",()=>{e.destroyed||!e.autoplay.running||(I=!0)}),Object.assign(e.autoplay,{start:C,stop:x,pause:O,resume:g})}const me=i=>{try{return document.querySelector(i)}catch{return null}},ze=i=>{try{return document.querySelectorAll(i)}catch{return document.querySelectorAll("")}},ke=i=>{try{return document.getElementById(i)}catch{return null}},q=(i,e={},t="")=>{try{const s=document.createElement(i);for(const[r,n]of Object.entries(e))r==="className"?s.className=String(n):r==="id"?s.id=String(n):s.setAttribute(r,String(n));return t&&(s.innerHTML=t),s}catch{return document.createElement("div")}},ie=(i,e)=>{try{i.appendChild(e)}catch{}},Ge=(i,e)=>{try{i.prepend(e)}catch{}},di=i=>{try{i.remove()}catch{}},Ne=(i,e)=>{try{for(const[t,s]of Object.entries(e))i.style.setProperty(t,String(s))}catch{}},Be={USER_AGENT_SUBSTR_START:0,USER_AGENT_SUBSTR_LENGTH:4},V={MOBILE:{SPACE_BETWEEN:80,SLIDES_PER_VIEW:4},DESKTOP:{SPACE_BETWEEN:10,SLIDES_PER_VIEW:7},AUTOPLAY_DELAY:3e3},ci={MAX_ERROR_LOG_ENTRIES:50,DOM_READY_TIMEOUT:5e3},B={EMPTY_LENGTH:0,FIRST_INDEX:0,NOT_FOUND_INDEX:-1,NEXT_ITEM_OFFSET:1,LAST_ITEM_OFFSET:-1},Re={CHECK_INTERVAL:10,DATA_CHECK_INTERVAL:100},Ve={SWIPER_TAG_CONTAINER_ID:"swiperTagContainer",SWIPER_TAG_WRAPPER_ID:"swiperTagWrapper"},Fe={ID:"wusong8899-flarum-tag-tiles",TRANSLATION_PREFIX:"wusong8899-tag-tiles"},he=()=>{try{return navigator.userAgent.substring(Be.USER_AGENT_SUBSTR_START,Be.USER_AGENT_SUBSTR_LENGTH)==="Mobi"}catch{return!1}},ui=()=>he()?{spaceBetween:V.MOBILE.SPACE_BETWEEN,slidesPerView:V.MOBILE.SLIDES_PER_VIEW}:{spaceBetween:V.DESKTOP.SPACE_BETWEEN,slidesPerView:V.DESKTOP.SLIDES_PER_VIEW},F={env:"production",app:{extensionId:Fe.ID,translationPrefix:Fe.TRANSLATION_PREFIX},tagTiles:{autoplayDelay:V.AUTOPLAY_DELAY,checkInterval:Re.CHECK_INTERVAL,dataCheckInterval:Re.DATA_CHECK_INTERVAL,mobile:{spaceBetween:V.MOBILE.SPACE_BETWEEN,slidesPerView:V.MOBILE.SLIDES_PER_VIEW},desktop:{spaceBetween:V.DESKTOP.SPACE_BETWEEN,slidesPerView:V.DESKTOP.SLIDES_PER_VIEW}},ui:{tagContainerId:Ve.SWIPER_TAG_CONTAINER_ID,tagWrapperId:Ve.SWIPER_TAG_WRAPPER_ID}};class fi{changeCategoryLayout(){try{if(ke(F.ui.tagContainerId))return;const e=ze(".TagTile");e.length>B.EMPTY_LENGTH?this.processTagTiles(e):this.waitForTagTilesAndProcess()}catch{}}waitForTagTilesAndProcess(){let s=0;const r=()=>{s+=B.NEXT_ITEM_OFFSET;const n=ze(".TagTile");n.length>B.EMPTY_LENGTH?this.processTagTiles(n):s<10&&setTimeout(r,200)};r()}processTagTiles(e){try{const t=this.createTagSwiperContainer();if(!t)return;const s=this.createTagSwiper(t);if(!s)return;const r=this.createTagSwiperWrapper(s);if(!r)return;this.populateTagSlides(r,e),this.appendTagContainer(t),this.addTagSwiperContent(t),this.removeOriginalTagTiles(),this.setupMobileStyles(),this.initializeTagSwiper(),this.notifyTagsLayoutChanged()}catch{}}createTagSwiperContainer(){const e=q("div",{className:"swiperTagContainer",id:F.ui.tagContainerId}),t=q("div",{className:"TagTextOuterContainer"});return ie(e,t),e}createTagSwiper(e){const t=e.querySelector(".TagTextOuterContainer"),s=q("div",{className:"swiper tagSwiper"});return t&&ie(t,s),s}createTagSwiperWrapper(e){const t=q("div",{className:"swiper-wrapper",id:F.ui.tagWrapperId});return ie(e,t),t}populateTagSlides(e,t){const s=he();for(const r of t){const n=r,o=this.extractTagData(n);if(o){const a=this.createTagSlide(o,s);ie(e,a)}}}extractTagData(e){const t=e.querySelector("a"),s=e.querySelector(".TagTile-name"),r=e.querySelector(".TagTile-description");if(!t||!s)return;const n=this.getTagBackgroundImage(t.href,e),o=globalThis.getComputedStyle(e),a=n||o.background;let d="",l="";return r&&(d=r.textContent||"",l=globalThis.getComputedStyle(r).color),{url:t.href,background:a,name:s.textContent||"",nameColor:globalThis.getComputedStyle(s).color,description:d,descColor:l}}getTagBackgroundImage(e,t){try{const r=new URL(e,globalThis.location.origin).pathname.split("/").filter(Boolean),n=r.indexOf("t"),o=r.indexOf("tags");let a="";if(n!==B.NOT_FOUND_INDEX&&r[n+B.NEXT_ITEM_OFFSET]?a=r[n+B.NEXT_ITEM_OFFSET]:o!==B.NOT_FOUND_INDEX&&r[o+B.NEXT_ITEM_OFFSET]?a=r[o+B.NEXT_ITEM_OFFSET]:r.length>B.EMPTY_LENGTH&&(a=r[r.length+B.LAST_ITEM_OFFSET]),!a)return;const d=this.getTagBackgroundUrlBySlug(a);return d?`url(${d})`:void 0}catch{const s=t.style.background;return s&&s.includes("url(")?s:void 0}}getTagBackgroundUrlBySlug(e){try{const s=X.store.all("tags").find(n=>{const o=n;let a="";return typeof o.slug=="function"?a=o.slug():o.attribute&&typeof o.attribute=="function"&&(a=o.attribute("slug")),a===e});if(!s)return;const r=s;if(r.attribute&&typeof r.attribute=="function"){const n=r.attribute("wusong8899BackgroundURL");if(n)return n}return}catch{return}}createTagSlide(e,t){const s=q("div",{className:"swiper-slide swiper-slide-tag"});let r="swiper-slide-tag-inner";t&&(r="swiper-slide-tag-inner-mobile");const n=`background:${e.background};background-size: cover;background-position: center;background-repeat: no-repeat;`,o=this.hasBackgroundImage(e.background);let a="";return o||(a=`
            <div style='font-weight:bold;font-size:14px;color:${e.nameColor}'>
                ${e.name}
            </div>
        `),s.innerHTML=`
            <a href='${e.url}'>
                <div class='${r}' style='${n}'>
                    ${a}
                </div>
            </a>
        `,s}hasBackgroundImage(e){return e?e.includes("url(")&&!e.includes("url()"):!1}appendTagContainer(e){const t=me("#content .container .TagsPage-content");t&&Ge(t,e)}addTagSwiperContent(e){const t=e.querySelector(".TagTextOuterContainer");if(t){const s=q("div",{className:"TagTextContainer"},"<div class='TagTextIcon'></div>中文玩家社区资讯");Ge(t,s);const r=this.createSocialButtonsHTML();t.insertAdjacentHTML("beforeend",r)}}createSocialButtonsHTML(){const e=F.app.extensionId,s=[{urlKey:`${e}.SocialKickUrl`,iconKey:`${e}.SocialKickIcon`,defaultIcon:""},{urlKey:`${e}.SocialFacebookUrl`,iconKey:`${e}.SocialFacebookIcon`,defaultIcon:""},{urlKey:`${e}.SocialTwitterUrl`,iconKey:`${e}.SocialTwitterIcon`,defaultIcon:""},{urlKey:`${e}.SocialYouTubeUrl`,iconKey:`${e}.SocialYouTubeIcon`,defaultIcon:""},{urlKey:`${e}.SocialInstagramUrl`,iconKey:`${e}.SocialInstagramIcon`,defaultIcon:""}].map((r,n)=>{const o=X.forum.attribute(r.urlKey)||"",a=X.forum.attribute(r.iconKey)||r.defaultIcon;if(!o.trim()||!a.trim())return"";let d="";return n>B.FIRST_INDEX&&(d="margin-left: 20px;"),`<img onClick="window.open('${o}', '_blank')" style="width: 32px;${d}" src="${a}">`}).filter(r=>r!=="").join("");return s?`
            <div style="text-align:center;padding-top: 10px;">
                <button class="Button Button--primary" type="button" style="font-weight: normal !important; color:#ffa000; background: #1a1d2e !important;border-radius: 2rem !important;">
                    <div style="margin-top: 5px;" class="Button-label">
                        ${s}
                    </div>
                </button>
            </div>
        `:""}removeOriginalTagTiles(){const e=me(".TagTiles");e&&di(e)}setupMobileStyles(){if(he()){const e=ke("app"),t=me(".App-content");e&&Ne(e,{"overflow-x":"hidden"}),t&&Ne(t,{"min-height":"auto",background:""})}}initializeTagSwiper(){try{const e=ui(),t=new N(".tagSwiper",{loop:!0,spaceBetween:e.spaceBetween,slidesPerView:e.slidesPerView,autoplay:{delay:V.AUTOPLAY_DELAY,disableOnInteraction:!1},modules:[li]})}catch{}}notifyTagsLayoutChanged(){try{const e=new CustomEvent("tagsLayoutChanged",{detail:{extensionId:F.app.extensionId,layoutType:"swiper"}});document.dispatchEvent(e)}catch{}}}class U{constructor(){this.errorLog=[],this.isInitialized=!1}static getInstance(){return U.instance||(U.instance=new U),U.instance}initialize(){try{return this.isInitialized||(this.setupGlobalErrorHandling(),this.isInitialized=!0),!0}catch{return!1}}handleSync(e,t){try{return e()}catch(s){this.logError(s,t);return}}async handleAsync(e,t){try{return await e()}catch(s){this.logError(s,t);return}}logError(e,t){try{const s={timestamp:new Date,error:e,context:t};this.errorLog.push(s),this.errorLog.length>ci.MAX_ERROR_LOG_ENTRIES&&this.errorLog.shift()}catch{}}setupGlobalErrorHandling(){try{window.addEventListener("unhandledrejection",e=>{this.logError(new Error(String(e.reason)),"Unhandled Promise Rejection")})}catch{}}getErrorLog(){return[...this.errorLog]}clearErrorLog(){this.errorLog=[]}}class K{constructor(){}static getInstance(){return K.instance||(K.instance=new K),K.instance}isTagsPage(){try{return X.current.get("routeName")==="tags"}catch{try{return window.location.pathname.includes("/tags")}catch{return!1}}}getConfig(){return F}isConfigured(){try{const e=["Kick","Facebook","Twitter","YouTube","Instagram"];for(const t of e){const s=X.forum.attribute(`${F.app.extensionId}.Social${t}Url`),r=X.forum.attribute(`${F.app.extensionId}.Social${t}Icon`);if(s&&r)return!0}return!1}catch{return!1}}}X.initializers.add(F.app.extensionId,()=>{const i=U.getInstance(),e=K.getInstance();if(!i.initialize())return;const t=new fi;ge.extend(ve.prototype,"oncreate",function(r){i.handleSync(()=>{e.isTagsPage()&&setTimeout(()=>{t.changeCategoryLayout()},100)},"TagsPage oncreate extension")}),ge.extend(ve.prototype,"onupdate",function(r){i.handleSync(()=>{document.getElementById(F.ui.tagContainerId)||setTimeout(()=>{t.changeCategoryLayout()},100)},"TagsPage onupdate extension")})})})(flarum.core.compat["common/extend"],flarum.core.compat["forum/app"],flarum.core.compat["tags/components/TagsPage"]);
//# sourceMappingURL=forum.js.map

module.exports={};