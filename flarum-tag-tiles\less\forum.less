/* TagTiles Extension Styles */

/* Swiper Tag Container */
.swiperTagContainer {
  width: 100%;
  margin-bottom: 20px;
}

.TagTextOuterContainer {
  width: 100%;
}

.TagTextContainer {
  text-align: center;
  padding: 10px 0;
  font-size: 16px;
  font-weight: bold;
  color: #fff;
}

.TagTextIcon {
  display: inline-block;
  margin-right: 8px;
}

/* Tag Swiper Styles */
.tagSwiper {
  width: 100%;
  padding: 20px 0;
}

.tagSwiper .swiper-wrapper {
  display: flex;
  align-items: center;
}

.tagSwiper .swiper-slide {
  text-align: center;
  font-size: 18px;
  background: transparent;
  display: flex;
  justify-content: center;
  align-items: center;
}

.swiper-slide-tag {
  height: 120px;
  border-radius: 8px;
  overflow: hidden;
  position: relative;
}

.swiper-slide-tag a {
  display: block;
  width: 100%;
  height: 100%;
  text-decoration: none;
}

.swiper-slide-tag-inner {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 8px;
  transition: transform 0.3s ease;
}

.swiper-slide-tag-inner:hover {
  transform: scale(1.05);
}

.swiper-slide-tag-inner-mobile {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 8px;
  font-size: 12px;
}

/* Social Media Buttons */
.Button--primary {
  border-radius: 2rem !important;
}

.Button-label img {
  width: 32px;
  cursor: pointer;
  transition: transform 0.2s ease;
}

.Button-label img:hover {
  transform: scale(1.1);
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .swiperTagContainer {
    margin: 0 -15px;
  }

  .tagSwiper {
    padding: 10px 0;
  }

  .swiper-slide-tag {
    height: 80px;
  }

  .TagTextContainer {
    font-size: 14px;
    padding: 8px 0;
  }
}
