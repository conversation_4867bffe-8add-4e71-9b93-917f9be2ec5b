# Flarum Tag Tiles Swiper

A Flarum extension that converts the default tag tiles layout into a modern swiper-based carousel with social media integration.

## Features

- **Tag Tiles Conversion**: Transforms standard tag tiles into an interactive swiper carousel
- **Background Image Support**: Integrates with flarum-tag-background extension for custom tag backgrounds
- **Social Media Integration**: Displays configurable social media buttons (Kick, Facebook, Twitter, YouTube, Instagram)
- **Mobile Responsive**: Optimized layout for mobile devices
- **Independent Operation**: Works standalone without dependencies on other custom extensions

## Installation

```bash
composer require wusong8899/flarum-tag-tiles
```

## Configuration

1. Enable the extension in your Flarum admin panel
2. Configure social media links and icons in the extension settings
3. The extension will automatically convert tag tiles on the tags page

## Requirements

- Flarum ^1.0
- flarum/tags extension

## License

MIT License
