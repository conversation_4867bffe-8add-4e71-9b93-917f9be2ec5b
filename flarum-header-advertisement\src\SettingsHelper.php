<?php

declare(strict_types=1);

namespace wusong8899\FlarumHeaderAdvertisement;

use <PERSON>larum\Extend;

class SettingsHelper
{
    /**
     * Generate settings configuration for advertisement slides
     *
     * @param int $maxSlides Maximum number of slides to configure
     * @return array<Extend\Settings> Array of Extend\Settings configurations
     */
    public static function generateSlideSettings(int $maxSlides = 30): array
    {
        $settings = [];

        // Transition time
        $settings[] = (new Extend\Settings())->serializeToForum(
            'wusong8899-flarum-header-advertisement.TransitionTime',
            'wusong8899-flarum-header-advertisement.TransitionTime'
        );

        // Header icon URL
        $settings[] = (new Extend\Settings())->serializeToForum(
            'wusong8899-flarum-header-advertisement.HeaderIconUrl',
            'wusong8899-flarum-header-advertisement.HeaderIconUrl'
        );

        // Generate settings for each slide
        for ($i = 1; $i <= $maxSlides; $i++) {
            // Link setting
            $settings[] = (new Extend\Settings())->serializeToForum(
                "wusong8899-flarum-header-advertisement.Link{$i}",
                "wusong8899-flarum-header-advertisement.Link{$i}"
            );

            // Image setting
            $settings[] = (new Extend\Settings())->serializeToForum(
                "wusong8899-flarum-header-advertisement.Image{$i}",
                "wusong8899-flarum-header-advertisement.Image{$i}"
            );
        }

        return $settings;
    }

    /**
     * Get frontend configuration
     *
     * @return array<Extend\Frontend|Extend\Locales> Array of frontend configurations
     */
    public static function getFrontendConfig(): array
    {
        return [
            (new Extend\Frontend('forum'))
                ->js(__DIR__ . '/../js/dist/forum.js')
                ->css(__DIR__ . '/../less/forum.less'),
            (new Extend\Frontend('admin'))
                ->js(__DIR__ . '/../js/dist/admin.js')
                ->css(__DIR__ . '/../less/admin.less'),
            new Extend\Locales(__DIR__ . '/../locale'),
        ];
    }

    /**
     * Get complete extension configuration
     *
     * @param int $maxSlides Maximum number of slides
     * @return array<Extend\Frontend|Extend\Locales|Extend\Settings> Complete configuration array
     */
    public static function getExtensionConfig(int $maxSlides = 30): array
    {
        return array_merge(
            self::getFrontendConfig(),
            self::generateSlideSettings($maxSlides)
        );
    }
}
