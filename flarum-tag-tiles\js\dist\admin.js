(function(t){"use strict";class s{constructor(e){this.extensionId=e,this.extensionData=t.extensionData.for(e)}registerSocialMediaSettings(){const e=["Kick","Facebook","Twitter","YouTube","Instagram"];for(const i of e)this.extensionData.registerSetting({setting:`${this.extensionId}.Social${i}Url`,type:"url",label:String(t.translator.trans(`wusong8899-tag-tiles.admin.Social${i}Url`)),help:String(t.translator.trans(`wusong8899-tag-tiles.admin.Social${i}UrlHelp`))}),this.extensionData.registerSetting({setting:`${this.extensionId}.Social${i}Icon`,type:"text",label:String(t.translator.trans(`wusong8899-tag-tiles.admin.Social${i}Icon`)),help:String(t.translator.trans(`wusong8899-tag-tiles.admin.Social${i}IconHelp`))});return this}registerAllSettings(){return this.registerSocialMediaSettings()}}const a={EXTENSION_ID:"wusong8899-flarum-tag-tiles"},r=(n=a.EXTENSION_ID)=>{new s(n).registerAllSettings()};t.initializers.add("wusong8899/flarum-tag-tiles",()=>{r()})})(flarum.core.compat["admin/app"]);
//# sourceMappingURL=admin.js.map

module.exports={};