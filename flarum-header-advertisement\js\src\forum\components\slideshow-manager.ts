import Swiper from 'swiper';
import { EffectCoverflow, Navigation, Pagination, Autoplay } from 'swiper/modules';
import app from 'flarum/forum/app';
import * as DOMUtils from '../utils/dom-utils';
import { isMobileDevice } from '../utils/mobile-detection';
import { defaultConfig } from '../../common/config';
import { MOBILE_LAYOUT, SLIDESHOW_CONSTANTS } from '../../common/config/constants';
import type { FlarumVnode } from '../../common/config/types';

/**
 * Slideshow manager for header advertisements
 */
export class SlideshowManager {
    private swiper: Swiper | undefined;
    private container: HTMLElement | undefined;
    private readonly maxSlides = defaultConfig.slider.maxSlides;
    private readonly checkTime = defaultConfig.slider.checkTime;

    /**
     * Safely read a forum attribute if available
     */
    private getForumAttribute(key: string): unknown {
        try {
            const forum = app && app.forum;
            const attrFn = forum && forum.attribute;
            if (typeof attrFn === 'function') {
                return attrFn.call(forum, key);
            }
            return;
        } catch {
            return;
        }
    }

    /**
     * Initialize and attach slideshow to the DOM
     */
    attachAdvertiseHeader(_vdom: FlarumVnode): void {
        try {
            this.destroy(); // Clean up any existing instance

            const container = this.createContainer();
            const swiper = this.createSwiperElement(container);
            const wrapper = this.createSwiperWrapper(swiper);

            this.populateSlides(wrapper);
            this.createPagination(swiper);
            this.createNavigation(swiper);

            this.container = container;
            this.appendToDOM(container);

            // Initialize Swiper after DOM attachment
            setTimeout(() => {
                this.initializeSwiper(this.getTransitionTime());
            }, this.checkTime);
        } catch {
            // Silently handle slideshow creation errors
        }
    }

    /**
     * Remove existing navigation elements
     */
    private removeExistingNavigation(): void {
        const existingContainer = DOMUtils.querySelector(`#${defaultConfig.slider.dom.containerId}`);
        if (existingContainer) {
            DOMUtils.removeElement(existingContainer);
        }

        const navElements = DOMUtils.querySelectorAll(".item-nav");
        for (const element of navElements) {
            DOMUtils.removeElement(element);
        }
    }

    /**
     * Create main container element
     * @returns Container element
     */
    private createContainer(): HTMLElement {
        this.removeExistingNavigation();

        const container = DOMUtils.createElement('div', {
            id: defaultConfig.slider.dom.containerId,
            className: 'adContainer'
        });

        this.applyMobileStyles(container);
        return container;
    }

    /**
     * Apply mobile-specific styles if needed
     * @param container - Container element
     */
    private applyMobileStyles(container: HTMLElement): void {
        if (isMobileDevice()) {
            const screenWidth = globalThis.innerWidth;
            const styleWidth = screenWidth * MOBILE_LAYOUT.SCREEN_WIDTH_MULTIPLIER - MOBILE_LAYOUT.SCREEN_WIDTH_OFFSET;
            DOMUtils.setStyles(container, {
                'width': `${styleWidth}px`,
                'margin-left': `${-(styleWidth * MOBILE_LAYOUT.CONTAINER_MARGIN_MULTIPLIER)}px`
            });
        }
    }

    /**
     * Create Swiper element
     * @param {HTMLElement} container - Parent container
     * @returns {HTMLElement} Swiper element
     */
    private createSwiperElement(container: HTMLElement): HTMLElement {
        const swiper = DOMUtils.createElement('div', {
            className: `swiper ${defaultConfig.slider.dom.swiperClass}`
        });
        DOMUtils.appendChild(container, swiper);
        return swiper;
    }

    /**
     * Create Swiper wrapper
     * @param {HTMLElement} swiper - Swiper element
     * @returns {HTMLElement} Wrapper element
     */
    private createSwiperWrapper(swiper: HTMLElement): HTMLElement {
        const wrapper = DOMUtils.createElement('div', {
            className: 'swiper-wrapper'
        });
        DOMUtils.appendChild(swiper, wrapper);
        return wrapper;
    }

    /**
     * Get transition time from forum settings
     * @returns Transition time in milliseconds
     */
    private getTransitionTime(): number {
        const transitionTime = this.getForumAttribute('wusong8899-flarum-header-advertisement.TransitionTime');
        if (transitionTime) {
            return Number.parseInt(String(transitionTime), 10);
        }
        return defaultConfig.slider.defaultTransitionTime;
    }

    /**
     * Populate slides with data from forum settings
     * @param {HTMLElement} wrapper - Swiper wrapper element
     */
    private populateSlides(wrapper: HTMLElement): void {
        for (let slideIndex = SLIDESHOW_CONSTANTS.INITIAL_SLIDE_INDEX; slideIndex <= this.maxSlides; slideIndex += SLIDESHOW_CONSTANTS.SLIDE_INCREMENT) {
            const imageSrc = this.getForumAttribute(`wusong8899-flarum-header-advertisement.Image${slideIndex}`);
            const imageLink = this.getForumAttribute(`wusong8899-flarum-header-advertisement.Link${slideIndex}`);

            if (imageSrc) {
                const slide = this.createSlide(String(imageSrc), String(imageLink || ''));
                DOMUtils.appendChild(wrapper, slide);
            }
        }
    }

    /**
     * Create individual slide
     * @param {string} imageSrc - Image source URL
     * @param {string} imageLink - Link URL
     * @returns {HTMLElement} Slide element
     */
    private createSlide(imageSrc: string, imageLink: string): HTMLElement {
        const slide = DOMUtils.createElement('div', {
            className: 'swiper-slide'
        });

        let clickHandler = '';
        if (imageLink) {
            clickHandler = `window.location.href="${imageLink}"`;
        }
        slide.innerHTML = `<img onclick='${clickHandler}' src='${imageSrc}' />`;

        return slide;
    }

    /**
     * Create pagination element
     * @param {HTMLElement} swiper - Swiper element
     */
    private createPagination(swiper: HTMLElement): void {
        const pagination = DOMUtils.createElement('div', {
            className: 'swiper-pagination'
        });
        DOMUtils.appendChild(swiper, pagination);
    }

    /**
     * Create navigation elements
     * @param {HTMLElement} swiper - Swiper element
     */
    private createNavigation(swiper: HTMLElement): void {
        const prevButton = DOMUtils.createElement('div', {
            className: 'swiper-button-prev'
        });
        const nextButton = DOMUtils.createElement('div', {
            className: 'swiper-button-next'
        });

        DOMUtils.appendChild(swiper, prevButton);
        DOMUtils.appendChild(swiper, nextButton);
    }

    /**
     * Append slideshow to DOM
     * @param {HTMLElement} container - Container element
     */
    private appendToDOM(container: HTMLElement): void {
        const contentContainer = DOMUtils.querySelector("#content .container");
        if (contentContainer) {
            DOMUtils.prependChild(contentContainer, container);
        }
    }

    /**
     * Initialize Swiper instance
     * @param {number} transitionTime - Transition time in milliseconds
     */
    private initializeSwiper(transitionTime: number): void {
        try {
            this.swiper = new Swiper(`.${defaultConfig.slider.dom.swiperClass}`, {
                autoplay: {
                    delay: transitionTime,
                },
                spaceBetween: defaultConfig.slider.swiper.spaceBetween,
                effect: defaultConfig.slider.swiper.effect,
                centeredSlides: defaultConfig.slider.swiper.centeredSlides,
                slidesPerView: defaultConfig.slider.swiper.slidesPerView,
                coverflowEffect: {
                    rotate: defaultConfig.slider.swiper.coverflowEffect.rotate,
                    depth: defaultConfig.slider.swiper.coverflowEffect.depth,
                    modifier: defaultConfig.slider.swiper.coverflowEffect.modifier,
                    slideShadows: defaultConfig.slider.swiper.coverflowEffect.slideShadows,
                    stretch: defaultConfig.slider.swiper.coverflowEffect.stretch,
                },
                pagination: {
                    el: defaultConfig.slider.swiper.pagination.el,
                    type: defaultConfig.slider.swiper.pagination.type,
                },
                navigation: {
                    nextEl: defaultConfig.slider.swiper.navigation.nextEl,
                    prevEl: defaultConfig.slider.swiper.navigation.prevEl,
                },
                modules: [EffectCoverflow, Navigation, Pagination, Autoplay]
            });
        } catch {
            // Silently handle Swiper initialization errors
        }
    }

    /**
     * Destroy slideshow instance
     */
    destroy(): void {
        if (this.swiper) {
            this.swiper.destroy(true, true);
            delete this.swiper;
        }

        if (this.container) {
            DOMUtils.removeElement(this.container);
            delete this.container;
        }
    }
}
