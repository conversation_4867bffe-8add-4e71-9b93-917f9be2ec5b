(function(he,<PERSON>,st,Ne,rt){"use strict";function De(s){return s!==null&&typeof s=="object"&&"constructor"in s&&s.constructor===Object}function ge(s,e){s===void 0&&(s={}),e===void 0&&(e={});const t=["__proto__","constructor","prototype"];Object.keys(e).filter(i=>t.indexOf(i)<0).forEach(i=>{typeof s[i]>"u"?s[i]=e[i]:De(e[i])&&De(s[i])&&Object.keys(e[i]).length>0&&ge(s[i],e[i])})}const ke={body:{},addEventListener(){},removeEventListener(){},activeElement:{blur(){},nodeName:""},querySelector(){return null},querySelectorAll(){return[]},getElementById(){return null},createEvent(){return{initEvent(){}}},createElement(){return{children:[],childNodes:[],style:{},setAttribute(){},getElementsByTagName(){return[]}}},createElementNS(){return{}},importNode(){return null},location:{hash:"",host:"",hostname:"",href:"",origin:"",pathname:"",protocol:"",search:""}};function U(){const s=typeof document<"u"?document:{};return ge(s,ke),s}const nt={document:ke,navigator:{userAgent:""},location:{hash:"",host:"",hostname:"",href:"",origin:"",pathname:"",protocol:"",search:""},history:{replaceState(){},pushState(){},go(){},back(){}},CustomEvent:function(){return this},addEventListener(){},removeEventListener(){},getComputedStyle(){return{getPropertyValue(){return""}}},Image(){},Date(){},screen:{},setTimeout(){},clearTimeout(){},matchMedia(){return{}},requestAnimationFrame(s){return typeof setTimeout>"u"?(s(),null):setTimeout(s,0)},cancelAnimationFrame(s){typeof setTimeout>"u"||clearTimeout(s)}};function $(){const s=typeof window<"u"?window:{};return ge(s,nt),s}function at(s){return s===void 0&&(s=""),s.trim().split(" ").filter(e=>!!e.trim())}function ot(s){const e=s;Object.keys(e).forEach(t=>{try{e[t]=null}catch{}try{delete e[t]}catch{}})}function Re(s,e){return e===void 0&&(e=0),setTimeout(s,e)}function se(){return Date.now()}function lt(s){const e=$();let t;return e.getComputedStyle&&(t=e.getComputedStyle(s,null)),!t&&s.currentStyle&&(t=s.currentStyle),t||(t=s.style),t}function dt(s,e){e===void 0&&(e="x");const t=$();let i,r,n;const o=lt(s);return t.WebKitCSSMatrix?(r=o.transform||o.webkitTransform,r.split(",").length>6&&(r=r.split(", ").map(a=>a.replace(",",".")).join(", ")),n=new t.WebKitCSSMatrix(r==="none"?"":r)):(n=o.MozTransform||o.OTransform||o.MsTransform||o.msTransform||o.transform||o.getPropertyValue("transform").replace("translate(","matrix(1, 0, 0, 1,"),i=n.toString().split(",")),e==="x"&&(t.WebKitCSSMatrix?r=n.m41:i.length===16?r=parseFloat(i[12]):r=parseFloat(i[4])),e==="y"&&(t.WebKitCSSMatrix?r=n.m42:i.length===16?r=parseFloat(i[13]):r=parseFloat(i[5])),r||0}function re(s){return typeof s=="object"&&s!==null&&s.constructor&&Object.prototype.toString.call(s).slice(8,-1)==="Object"}function ct(s){return typeof window<"u"&&typeof window.HTMLElement<"u"?s instanceof HTMLElement:s&&(s.nodeType===1||s.nodeType===11)}function F(){const s=Object(arguments.length<=0?void 0:arguments[0]),e=["__proto__","constructor","prototype"];for(let t=1;t<arguments.length;t+=1){const i=t<0||arguments.length<=t?void 0:arguments[t];if(i!=null&&!ct(i)){const r=Object.keys(Object(i)).filter(n=>e.indexOf(n)<0);for(let n=0,o=r.length;n<o;n+=1){const a=r[n],d=Object.getOwnPropertyDescriptor(i,a);d!==void 0&&d.enumerable&&(re(s[a])&&re(i[a])?i[a].__swiper__?s[a]=i[a]:F(s[a],i[a]):!re(s[a])&&re(i[a])?(s[a]={},i[a].__swiper__?s[a]=i[a]:F(s[a],i[a])):s[a]=i[a])}}}return s}function ne(s,e,t){s.style.setProperty(e,t)}function ze(s){let{swiper:e,targetPosition:t,side:i}=s;const r=$(),n=-e.translate;let o=null,a;const d=e.params.speed;e.wrapperEl.style.scrollSnapType="none",r.cancelAnimationFrame(e.cssModeFrameID);const l=t>n?"next":"prev",u=(h,g)=>l==="next"&&h>=g||l==="prev"&&h<=g,p=()=>{a=new Date().getTime(),o===null&&(o=a);const h=Math.max(Math.min((a-o)/d,1),0),g=.5-Math.cos(h*Math.PI)/2;let m=n+g*(t-n);if(u(m,t)&&(m=t),e.wrapperEl.scrollTo({[i]:m}),u(m,t)){e.wrapperEl.style.overflow="hidden",e.wrapperEl.style.scrollSnapType="",setTimeout(()=>{e.wrapperEl.style.overflow="",e.wrapperEl.scrollTo({[i]:m})}),r.cancelAnimationFrame(e.cssModeFrameID);return}e.cssModeFrameID=r.requestAnimationFrame(p)};p()}function ve(s){return s.querySelector(".swiper-slide-transform")||s.shadowRoot&&s.shadowRoot.querySelector(".swiper-slide-transform")||s}function j(s,e){e===void 0&&(e="");const t=$(),i=[...s.children];return t.HTMLSlotElement&&s instanceof HTMLSlotElement&&i.push(...s.assignedElements()),e?i.filter(r=>r.matches(e)):i}function ut(s,e){const t=[e];for(;t.length>0;){const i=t.shift();if(s===i)return!0;t.push(...i.children,...i.shadowRoot?i.shadowRoot.children:[],...i.assignedElements?i.assignedElements():[])}}function ft(s,e){const t=$();let i=e.contains(s);return!i&&t.HTMLSlotElement&&e instanceof HTMLSlotElement&&(i=[...e.assignedElements()].includes(s),i||(i=ut(s,e))),i}function ae(s){try{console.warn(s);return}catch{}}function ee(s,e){e===void 0&&(e=[]);const t=document.createElement(s);return t.classList.add(...Array.isArray(e)?e:at(e)),t}function pt(s,e){const t=[];for(;s.previousElementSibling;){const i=s.previousElementSibling;e?i.matches(e)&&t.push(i):t.push(i),s=i}return t}function mt(s,e){const t=[];for(;s.nextElementSibling;){const i=s.nextElementSibling;e?i.matches(e)&&t.push(i):t.push(i),s=i}return t}function K(s,e){return $().getComputedStyle(s,null).getPropertyValue(e)}function oe(s){let e=s,t;if(e){for(t=0;(e=e.previousSibling)!==null;)e.nodeType===1&&(t+=1);return t}}function Be(s,e){const t=[];let i=s.parentElement;for(;i;)e?i.matches(e)&&t.push(i):t.push(i),i=i.parentElement;return t}function we(s,e,t){const i=$();return s[e==="width"?"offsetWidth":"offsetHeight"]+parseFloat(i.getComputedStyle(s,null).getPropertyValue(e==="width"?"margin-right":"margin-top"))+parseFloat(i.getComputedStyle(s,null).getPropertyValue(e==="width"?"margin-left":"margin-bottom"))}function B(s){return(Array.isArray(s)?s:[s]).filter(e=>!!e)}function ht(s){return e=>Math.abs(e)>0&&s.browser&&s.browser.need3dFix&&Math.abs(e)%90===0?e+.001:e}function $e(s,e){e===void 0&&(e=""),typeof trustedTypes<"u"?s.innerHTML=trustedTypes.createPolicy("html",{createHTML:t=>t}).createHTML(e):s.innerHTML=e}let Se;function gt(){const s=$(),e=U();return{smoothScroll:e.documentElement&&e.documentElement.style&&"scrollBehavior"in e.documentElement.style,touch:!!("ontouchstart"in s||s.DocumentTouch&&e instanceof s.DocumentTouch)}}function Ge(){return Se||(Se=gt()),Se}let Te;function vt(s){let{userAgent:e}=s===void 0?{}:s;const t=Ge(),i=$(),r=i.navigator.platform,n=e||i.navigator.userAgent,o={ios:!1,android:!1},a=i.screen.width,d=i.screen.height,l=n.match(/(Android);?[\s\/]+([\d.]+)?/);let u=n.match(/(iPad).*OS\s([\d_]+)/);const p=n.match(/(iPod)(.*OS\s([\d_]+))?/),h=!u&&n.match(/(iPhone\sOS|iOS)\s([\d_]+)/),g=r==="Win32";let m=r==="MacIntel";const v=["1024x1366","1366x1024","834x1194","1194x834","834x1112","1112x834","768x1024","1024x768","820x1180","1180x820","810x1080","1080x810"];return!u&&m&&t.touch&&v.indexOf(`${a}x${d}`)>=0&&(u=n.match(/(Version)\/([\d.]+)/),u||(u=[0,1,"13_0_0"]),m=!1),l&&!g&&(o.os="android",o.android=!0),(u||h||p)&&(o.os="ios",o.ios=!0),o}function Fe(s){return s===void 0&&(s={}),Te||(Te=vt(s)),Te}let ye;function wt(){const s=$(),e=Fe();let t=!1;function i(){const a=s.navigator.userAgent.toLowerCase();return a.indexOf("safari")>=0&&a.indexOf("chrome")<0&&a.indexOf("android")<0}if(i()){const a=String(s.navigator.userAgent);if(a.includes("Version/")){const[d,l]=a.split("Version/")[1].split(" ")[0].split(".").map(u=>Number(u));t=d<16||d===16&&l<2}}const r=/(iPhone|iPod|iPad).*AppleWebKit(?!.*Safari)/i.test(s.navigator.userAgent),n=i(),o=n||r&&e.ios;return{isSafari:t||n,needPerspectiveFix:t,need3dFix:o,isWebView:r}}function Ve(){return ye||(ye=wt()),ye}function St(s){let{swiper:e,on:t,emit:i}=s;const r=$();let n=null,o=null;const a=()=>{!e||e.destroyed||!e.initialized||(i("beforeResize"),i("resize"))},d=()=>{!e||e.destroyed||!e.initialized||(n=new ResizeObserver(p=>{o=r.requestAnimationFrame(()=>{const{width:h,height:g}=e;let m=h,v=g;p.forEach(b=>{let{contentBoxSize:S,contentRect:c,target:f}=b;f&&f!==e.el||(m=c?c.width:(S[0]||S).inlineSize,v=c?c.height:(S[0]||S).blockSize)}),(m!==h||v!==g)&&a()})}),n.observe(e.el))},l=()=>{o&&r.cancelAnimationFrame(o),n&&n.unobserve&&e.el&&(n.unobserve(e.el),n=null)},u=()=>{!e||e.destroyed||!e.initialized||i("orientationchange")};t("init",()=>{if(e.params.resizeObserver&&typeof r.ResizeObserver<"u"){d();return}r.addEventListener("resize",a),r.addEventListener("orientationchange",u)}),t("destroy",()=>{l(),r.removeEventListener("resize",a),r.removeEventListener("orientationchange",u)})}function Tt(s){let{swiper:e,extendParams:t,on:i,emit:r}=s;const n=[],o=$(),a=function(u,p){p===void 0&&(p={});const h=o.MutationObserver||o.WebkitMutationObserver,g=new h(m=>{if(e.__preventObserver__)return;if(m.length===1){r("observerUpdate",m[0]);return}const v=function(){r("observerUpdate",m[0])};o.requestAnimationFrame?o.requestAnimationFrame(v):o.setTimeout(v,0)});g.observe(u,{attributes:typeof p.attributes>"u"?!0:p.attributes,childList:e.isElement||(typeof p.childList>"u"?!0:p).childList,characterData:typeof p.characterData>"u"?!0:p.characterData}),n.push(g)},d=()=>{if(e.params.observer){if(e.params.observeParents){const u=Be(e.hostEl);for(let p=0;p<u.length;p+=1)a(u[p])}a(e.hostEl,{childList:e.params.observeSlideChildren}),a(e.wrapperEl,{attributes:!1})}},l=()=>{n.forEach(u=>{u.disconnect()}),n.splice(0,n.length)};t({observer:!1,observeParents:!1,observeSlideChildren:!1}),i("init",d),i("destroy",l)}var yt={on(s,e,t){const i=this;if(!i.eventsListeners||i.destroyed||typeof e!="function")return i;const r=t?"unshift":"push";return s.split(" ").forEach(n=>{i.eventsListeners[n]||(i.eventsListeners[n]=[]),i.eventsListeners[n][r](e)}),i},once(s,e,t){const i=this;if(!i.eventsListeners||i.destroyed||typeof e!="function")return i;function r(){i.off(s,r),r.__emitterProxy&&delete r.__emitterProxy;for(var n=arguments.length,o=new Array(n),a=0;a<n;a++)o[a]=arguments[a];e.apply(i,o)}return r.__emitterProxy=e,i.on(s,r,t)},onAny(s,e){const t=this;if(!t.eventsListeners||t.destroyed||typeof s!="function")return t;const i=e?"unshift":"push";return t.eventsAnyListeners.indexOf(s)<0&&t.eventsAnyListeners[i](s),t},offAny(s){const e=this;if(!e.eventsListeners||e.destroyed||!e.eventsAnyListeners)return e;const t=e.eventsAnyListeners.indexOf(s);return t>=0&&e.eventsAnyListeners.splice(t,1),e},off(s,e){const t=this;return!t.eventsListeners||t.destroyed||!t.eventsListeners||s.split(" ").forEach(i=>{typeof e>"u"?t.eventsListeners[i]=[]:t.eventsListeners[i]&&t.eventsListeners[i].forEach((r,n)=>{(r===e||r.__emitterProxy&&r.__emitterProxy===e)&&t.eventsListeners[i].splice(n,1)})}),t},emit(){const s=this;if(!s.eventsListeners||s.destroyed||!s.eventsListeners)return s;let e,t,i;for(var r=arguments.length,n=new Array(r),o=0;o<r;o++)n[o]=arguments[o];return typeof n[0]=="string"||Array.isArray(n[0])?(e=n[0],t=n.slice(1,n.length),i=s):(e=n[0].events,t=n[0].data,i=n[0].context||s),t.unshift(i),(Array.isArray(e)?e:e.split(" ")).forEach(d=>{s.eventsAnyListeners&&s.eventsAnyListeners.length&&s.eventsAnyListeners.forEach(l=>{l.apply(i,[d,...t])}),s.eventsListeners&&s.eventsListeners[d]&&s.eventsListeners[d].forEach(l=>{l.apply(i,t)})}),s}};function Et(){const s=this;let e,t;const i=s.el;typeof s.params.width<"u"&&s.params.width!==null?e=s.params.width:e=i.clientWidth,typeof s.params.height<"u"&&s.params.height!==null?t=s.params.height:t=i.clientHeight,!(e===0&&s.isHorizontal()||t===0&&s.isVertical())&&(e=e-parseInt(K(i,"padding-left")||0,10)-parseInt(K(i,"padding-right")||0,10),t=t-parseInt(K(i,"padding-top")||0,10)-parseInt(K(i,"padding-bottom")||0,10),Number.isNaN(e)&&(e=0),Number.isNaN(t)&&(t=0),Object.assign(s,{width:e,height:t,size:s.isHorizontal()?e:t}))}function bt(){const s=this;function e(y,E){return parseFloat(y.getPropertyValue(s.getDirectionLabel(E))||0)}const t=s.params,{wrapperEl:i,slidesEl:r,size:n,rtlTranslate:o,wrongRTL:a}=s,d=s.virtual&&t.virtual.enabled,l=d?s.virtual.slides.length:s.slides.length,u=j(r,`.${s.params.slideClass}, swiper-slide`),p=d?s.virtual.slides.length:u.length;let h=[];const g=[],m=[];let v=t.slidesOffsetBefore;typeof v=="function"&&(v=t.slidesOffsetBefore.call(s));let b=t.slidesOffsetAfter;typeof b=="function"&&(b=t.slidesOffsetAfter.call(s));const S=s.snapGrid.length,c=s.slidesGrid.length;let f=t.spaceBetween,w=-v,T=0,x=0;if(typeof n>"u")return;typeof f=="string"&&f.indexOf("%")>=0?f=parseFloat(f.replace("%",""))/100*n:typeof f=="string"&&(f=parseFloat(f)),s.virtualSize=-f,u.forEach(y=>{o?y.style.marginLeft="":y.style.marginRight="",y.style.marginBottom="",y.style.marginTop=""}),t.centeredSlides&&t.cssMode&&(ne(i,"--swiper-centered-offset-before",""),ne(i,"--swiper-centered-offset-after",""));const I=t.grid&&t.grid.rows>1&&s.grid;I?s.grid.initSlides(u):s.grid&&s.grid.unsetSlides();let C;const L=t.slidesPerView==="auto"&&t.breakpoints&&Object.keys(t.breakpoints).filter(y=>typeof t.breakpoints[y].slidesPerView<"u").length>0;for(let y=0;y<p;y+=1){C=0;let E;if(u[y]&&(E=u[y]),I&&s.grid.updateSlide(y,E,u),!(u[y]&&K(E,"display")==="none")){if(t.slidesPerView==="auto"){L&&(u[y].style[s.getDirectionLabel("width")]="");const P=getComputedStyle(E),O=E.style.transform,_=E.style.webkitTransform;if(O&&(E.style.transform="none"),_&&(E.style.webkitTransform="none"),t.roundLengths)C=s.isHorizontal()?we(E,"width"):we(E,"height");else{const D=e(P,"width"),A=e(P,"padding-left"),k=e(P,"padding-right"),M=e(P,"margin-left"),z=e(P,"margin-right"),G=P.getPropertyValue("box-sizing");if(G&&G==="border-box")C=D+M+z;else{const{clientWidth:Z,offsetWidth:me}=E;C=D+A+k+M+z+(me-Z)}}O&&(E.style.transform=O),_&&(E.style.webkitTransform=_),t.roundLengths&&(C=Math.floor(C))}else C=(n-(t.slidesPerView-1)*f)/t.slidesPerView,t.roundLengths&&(C=Math.floor(C)),u[y]&&(u[y].style[s.getDirectionLabel("width")]=`${C}px`);u[y]&&(u[y].swiperSlideSize=C),m.push(C),t.centeredSlides?(w=w+C/2+T/2+f,T===0&&y!==0&&(w=w-n/2-f),y===0&&(w=w-n/2-f),Math.abs(w)<1/1e3&&(w=0),t.roundLengths&&(w=Math.floor(w)),x%t.slidesPerGroup===0&&h.push(w),g.push(w)):(t.roundLengths&&(w=Math.floor(w)),(x-Math.min(s.params.slidesPerGroupSkip,x))%s.params.slidesPerGroup===0&&h.push(w),g.push(w),w=w+C+f),s.virtualSize+=C+f,T=C,x+=1}}if(s.virtualSize=Math.max(s.virtualSize,n)+b,o&&a&&(t.effect==="slide"||t.effect==="coverflow")&&(i.style.width=`${s.virtualSize+f}px`),t.setWrapperSize&&(i.style[s.getDirectionLabel("width")]=`${s.virtualSize+f}px`),I&&s.grid.updateWrapperSize(C,h),!t.centeredSlides){const y=[];for(let E=0;E<h.length;E+=1){let P=h[E];t.roundLengths&&(P=Math.floor(P)),h[E]<=s.virtualSize-n&&y.push(P)}h=y,Math.floor(s.virtualSize-n)-Math.floor(h[h.length-1])>1&&h.push(s.virtualSize-n)}if(d&&t.loop){const y=m[0]+f;if(t.slidesPerGroup>1){const E=Math.ceil((s.virtual.slidesBefore+s.virtual.slidesAfter)/t.slidesPerGroup),P=y*t.slidesPerGroup;for(let O=0;O<E;O+=1)h.push(h[h.length-1]+P)}for(let E=0;E<s.virtual.slidesBefore+s.virtual.slidesAfter;E+=1)t.slidesPerGroup===1&&h.push(h[h.length-1]+y),g.push(g[g.length-1]+y),s.virtualSize+=y}if(h.length===0&&(h=[0]),f!==0){const y=s.isHorizontal()&&o?"marginLeft":s.getDirectionLabel("marginRight");u.filter((E,P)=>!t.cssMode||t.loop?!0:P!==u.length-1).forEach(E=>{E.style[y]=`${f}px`})}if(t.centeredSlides&&t.centeredSlidesBounds){let y=0;m.forEach(P=>{y+=P+(f||0)}),y-=f;const E=y>n?y-n:0;h=h.map(P=>P<=0?-v:P>E?E+b:P)}if(t.centerInsufficientSlides){let y=0;m.forEach(P=>{y+=P+(f||0)}),y-=f;const E=(t.slidesOffsetBefore||0)+(t.slidesOffsetAfter||0);if(y+E<n){const P=(n-y-E)/2;h.forEach((O,_)=>{h[_]=O-P}),g.forEach((O,_)=>{g[_]=O+P})}}if(Object.assign(s,{slides:u,snapGrid:h,slidesGrid:g,slidesSizesGrid:m}),t.centeredSlides&&t.cssMode&&!t.centeredSlidesBounds){ne(i,"--swiper-centered-offset-before",`${-h[0]}px`),ne(i,"--swiper-centered-offset-after",`${s.size/2-m[m.length-1]/2}px`);const y=-s.snapGrid[0],E=-s.slidesGrid[0];s.snapGrid=s.snapGrid.map(P=>P+y),s.slidesGrid=s.slidesGrid.map(P=>P+E)}if(p!==l&&s.emit("slidesLengthChange"),h.length!==S&&(s.params.watchOverflow&&s.checkOverflow(),s.emit("snapGridLengthChange")),g.length!==c&&s.emit("slidesGridLengthChange"),t.watchSlidesProgress&&s.updateSlidesOffset(),s.emit("slidesUpdated"),!d&&!t.cssMode&&(t.effect==="slide"||t.effect==="fade")){const y=`${t.containerModifierClass}backface-hidden`,E=s.el.classList.contains(y);p<=t.maxBackfaceHiddenSlides?E||s.el.classList.add(y):E&&s.el.classList.remove(y)}}function It(s){const e=this,t=[],i=e.virtual&&e.params.virtual.enabled;let r=0,n;typeof s=="number"?e.setTransition(s):s===!0&&e.setTransition(e.params.speed);const o=a=>i?e.slides[e.getSlideIndexByData(a)]:e.slides[a];if(e.params.slidesPerView!=="auto"&&e.params.slidesPerView>1)if(e.params.centeredSlides)(e.visibleSlides||[]).forEach(a=>{t.push(a)});else for(n=0;n<Math.ceil(e.params.slidesPerView);n+=1){const a=e.activeIndex+n;if(a>e.slides.length&&!i)break;t.push(o(a))}else t.push(o(e.activeIndex));for(n=0;n<t.length;n+=1)if(typeof t[n]<"u"){const a=t[n].offsetHeight;r=a>r?a:r}(r||r===0)&&(e.wrapperEl.style.height=`${r}px`)}function xt(){const s=this,e=s.slides,t=s.isElement?s.isHorizontal()?s.wrapperEl.offsetLeft:s.wrapperEl.offsetTop:0;for(let i=0;i<e.length;i+=1)e[i].swiperSlideOffset=(s.isHorizontal()?e[i].offsetLeft:e[i].offsetTop)-t-s.cssOverflowAdjustment()}const He=(s,e,t)=>{e&&!s.classList.contains(t)?s.classList.add(t):!e&&s.classList.contains(t)&&s.classList.remove(t)};function Ct(s){s===void 0&&(s=this&&this.translate||0);const e=this,t=e.params,{slides:i,rtlTranslate:r,snapGrid:n}=e;if(i.length===0)return;typeof i[0].swiperSlideOffset>"u"&&e.updateSlidesOffset();let o=-s;r&&(o=s),e.visibleSlidesIndexes=[],e.visibleSlides=[];let a=t.spaceBetween;typeof a=="string"&&a.indexOf("%")>=0?a=parseFloat(a.replace("%",""))/100*e.size:typeof a=="string"&&(a=parseFloat(a));for(let d=0;d<i.length;d+=1){const l=i[d];let u=l.swiperSlideOffset;t.cssMode&&t.centeredSlides&&(u-=i[0].swiperSlideOffset);const p=(o+(t.centeredSlides?e.minTranslate():0)-u)/(l.swiperSlideSize+a),h=(o-n[0]+(t.centeredSlides?e.minTranslate():0)-u)/(l.swiperSlideSize+a),g=-(o-u),m=g+e.slidesSizesGrid[d],v=g>=0&&g<=e.size-e.slidesSizesGrid[d],b=g>=0&&g<e.size-1||m>1&&m<=e.size||g<=0&&m>=e.size;b&&(e.visibleSlides.push(l),e.visibleSlidesIndexes.push(d)),He(l,b,t.slideVisibleClass),He(l,v,t.slideFullyVisibleClass),l.progress=r?-p:p,l.originalProgress=r?-h:h}}function Mt(s){const e=this;if(typeof s>"u"){const u=e.rtlTranslate?-1:1;s=e&&e.translate&&e.translate*u||0}const t=e.params,i=e.maxTranslate()-e.minTranslate();let{progress:r,isBeginning:n,isEnd:o,progressLoop:a}=e;const d=n,l=o;if(i===0)r=0,n=!0,o=!0;else{r=(s-e.minTranslate())/i;const u=Math.abs(s-e.minTranslate())<1,p=Math.abs(s-e.maxTranslate())<1;n=u||r<=0,o=p||r>=1,u&&(r=0),p&&(r=1)}if(t.loop){const u=e.getSlideIndexByData(0),p=e.getSlideIndexByData(e.slides.length-1),h=e.slidesGrid[u],g=e.slidesGrid[p],m=e.slidesGrid[e.slidesGrid.length-1],v=Math.abs(s);v>=h?a=(v-h)/m:a=(v+m-g)/m,a>1&&(a-=1)}Object.assign(e,{progress:r,progressLoop:a,isBeginning:n,isEnd:o}),(t.watchSlidesProgress||t.centeredSlides&&t.autoHeight)&&e.updateSlidesProgress(s),n&&!d&&e.emit("reachBeginning toEdge"),o&&!l&&e.emit("reachEnd toEdge"),(d&&!n||l&&!o)&&e.emit("fromEdge"),e.emit("progress",r)}const Ee=(s,e,t)=>{e&&!s.classList.contains(t)?s.classList.add(t):!e&&s.classList.contains(t)&&s.classList.remove(t)};function Lt(){const s=this,{slides:e,params:t,slidesEl:i,activeIndex:r}=s,n=s.virtual&&t.virtual.enabled,o=s.grid&&t.grid&&t.grid.rows>1,a=p=>j(i,`.${t.slideClass}${p}, swiper-slide${p}`)[0];let d,l,u;if(n)if(t.loop){let p=r-s.virtual.slidesBefore;p<0&&(p=s.virtual.slides.length+p),p>=s.virtual.slides.length&&(p-=s.virtual.slides.length),d=a(`[data-swiper-slide-index="${p}"]`)}else d=a(`[data-swiper-slide-index="${r}"]`);else o?(d=e.find(p=>p.column===r),u=e.find(p=>p.column===r+1),l=e.find(p=>p.column===r-1)):d=e[r];d&&(o||(u=mt(d,`.${t.slideClass}, swiper-slide`)[0],t.loop&&!u&&(u=e[0]),l=pt(d,`.${t.slideClass}, swiper-slide`)[0],t.loop&&!l===0&&(l=e[e.length-1]))),e.forEach(p=>{Ee(p,p===d,t.slideActiveClass),Ee(p,p===u,t.slideNextClass),Ee(p,p===l,t.slidePrevClass)}),s.emitSlidesClasses()}const le=(s,e)=>{if(!s||s.destroyed||!s.params)return;const t=()=>s.isElement?"swiper-slide":`.${s.params.slideClass}`,i=e.closest(t());if(i){let r=i.querySelector(`.${s.params.lazyPreloaderClass}`);!r&&s.isElement&&(i.shadowRoot?r=i.shadowRoot.querySelector(`.${s.params.lazyPreloaderClass}`):requestAnimationFrame(()=>{i.shadowRoot&&(r=i.shadowRoot.querySelector(`.${s.params.lazyPreloaderClass}`),r&&r.remove())})),r&&r.remove()}},be=(s,e)=>{if(!s.slides[e])return;const t=s.slides[e].querySelector('[loading="lazy"]');t&&t.removeAttribute("loading")},Ie=s=>{if(!s||s.destroyed||!s.params)return;let e=s.params.lazyPreloadPrevNext;const t=s.slides.length;if(!t||!e||e<0)return;e=Math.min(e,t);const i=s.params.slidesPerView==="auto"?s.slidesPerViewDynamic():Math.ceil(s.params.slidesPerView),r=s.activeIndex;if(s.params.grid&&s.params.grid.rows>1){const o=r,a=[o-e];a.push(...Array.from({length:e}).map((d,l)=>o+i+l)),s.slides.forEach((d,l)=>{a.includes(d.column)&&be(s,l)});return}const n=r+i-1;if(s.params.rewind||s.params.loop)for(let o=r-e;o<=n+e;o+=1){const a=(o%t+t)%t;(a<r||a>n)&&be(s,a)}else for(let o=Math.max(r-e,0);o<=Math.min(n+e,t-1);o+=1)o!==r&&(o>n||o<r)&&be(s,o)};function Pt(s){const{slidesGrid:e,params:t}=s,i=s.rtlTranslate?s.translate:-s.translate;let r;for(let n=0;n<e.length;n+=1)typeof e[n+1]<"u"?i>=e[n]&&i<e[n+1]-(e[n+1]-e[n])/2?r=n:i>=e[n]&&i<e[n+1]&&(r=n+1):i>=e[n]&&(r=n);return t.normalizeSlideIndex&&(r<0||typeof r>"u")&&(r=0),r}function At(s){const e=this,t=e.rtlTranslate?e.translate:-e.translate,{snapGrid:i,params:r,activeIndex:n,realIndex:o,snapIndex:a}=e;let d=s,l;const u=g=>{let m=g-e.virtual.slidesBefore;return m<0&&(m=e.virtual.slides.length+m),m>=e.virtual.slides.length&&(m-=e.virtual.slides.length),m};if(typeof d>"u"&&(d=Pt(e)),i.indexOf(t)>=0)l=i.indexOf(t);else{const g=Math.min(r.slidesPerGroupSkip,d);l=g+Math.floor((d-g)/r.slidesPerGroup)}if(l>=i.length&&(l=i.length-1),d===n&&!e.params.loop){l!==a&&(e.snapIndex=l,e.emit("snapIndexChange"));return}if(d===n&&e.params.loop&&e.virtual&&e.params.virtual.enabled){e.realIndex=u(d);return}const p=e.grid&&r.grid&&r.grid.rows>1;let h;if(e.virtual&&r.virtual.enabled&&r.loop)h=u(d);else if(p){const g=e.slides.find(v=>v.column===d);let m=parseInt(g.getAttribute("data-swiper-slide-index"),10);Number.isNaN(m)&&(m=Math.max(e.slides.indexOf(g),0)),h=Math.floor(m/r.grid.rows)}else if(e.slides[d]){const g=e.slides[d].getAttribute("data-swiper-slide-index");g?h=parseInt(g,10):h=d}else h=d;Object.assign(e,{previousSnapIndex:a,snapIndex:l,previousRealIndex:o,realIndex:h,previousIndex:n,activeIndex:d}),e.initialized&&Ie(e),e.emit("activeIndexChange"),e.emit("snapIndexChange"),(e.initialized||e.params.runCallbacksOnInit)&&(o!==h&&e.emit("realIndexChange"),e.emit("slideChange"))}function Ot(s,e){const t=this,i=t.params;let r=s.closest(`.${i.slideClass}, swiper-slide`);!r&&t.isElement&&e&&e.length>1&&e.includes(s)&&[...e.slice(e.indexOf(s)+1,e.length)].forEach(a=>{!r&&a.matches&&a.matches(`.${i.slideClass}, swiper-slide`)&&(r=a)});let n=!1,o;if(r){for(let a=0;a<t.slides.length;a+=1)if(t.slides[a]===r){n=!0,o=a;break}}if(r&&n)t.clickedSlide=r,t.virtual&&t.params.virtual.enabled?t.clickedIndex=parseInt(r.getAttribute("data-swiper-slide-index"),10):t.clickedIndex=o;else{t.clickedSlide=void 0,t.clickedIndex=void 0;return}i.slideToClickedSlide&&t.clickedIndex!==void 0&&t.clickedIndex!==t.activeIndex&&t.slideToClickedSlide()}var _t={updateSize:Et,updateSlides:bt,updateAutoHeight:It,updateSlidesOffset:xt,updateSlidesProgress:Ct,updateProgress:Mt,updateSlidesClasses:Lt,updateActiveIndex:At,updateClickedSlide:Ot};function Nt(s){s===void 0&&(s=this.isHorizontal()?"x":"y");const e=this,{params:t,rtlTranslate:i,translate:r,wrapperEl:n}=e;if(t.virtualTranslate)return i?-r:r;if(t.cssMode)return r;let o=dt(n,s);return o+=e.cssOverflowAdjustment(),i&&(o=-o),o||0}function Dt(s,e){const t=this,{rtlTranslate:i,params:r,wrapperEl:n,progress:o}=t;let a=0,d=0;const l=0;t.isHorizontal()?a=i?-s:s:d=s,r.roundLengths&&(a=Math.floor(a),d=Math.floor(d)),t.previousTranslate=t.translate,t.translate=t.isHorizontal()?a:d,r.cssMode?n[t.isHorizontal()?"scrollLeft":"scrollTop"]=t.isHorizontal()?-a:-d:r.virtualTranslate||(t.isHorizontal()?a-=t.cssOverflowAdjustment():d-=t.cssOverflowAdjustment(),n.style.transform=`translate3d(${a}px, ${d}px, ${l}px)`);let u;const p=t.maxTranslate()-t.minTranslate();p===0?u=0:u=(s-t.minTranslate())/p,u!==o&&t.updateProgress(s),t.emit("setTranslate",t.translate,e)}function kt(){return-this.snapGrid[0]}function Rt(){return-this.snapGrid[this.snapGrid.length-1]}function zt(s,e,t,i,r){s===void 0&&(s=0),e===void 0&&(e=this.params.speed),t===void 0&&(t=!0),i===void 0&&(i=!0);const n=this,{params:o,wrapperEl:a}=n;if(n.animating&&o.preventInteractionOnTransition)return!1;const d=n.minTranslate(),l=n.maxTranslate();let u;if(i&&s>d?u=d:i&&s<l?u=l:u=s,n.updateProgress(u),o.cssMode){const p=n.isHorizontal();if(e===0)a[p?"scrollLeft":"scrollTop"]=-u;else{if(!n.support.smoothScroll)return ze({swiper:n,targetPosition:-u,side:p?"left":"top"}),!0;a.scrollTo({[p?"left":"top"]:-u,behavior:"smooth"})}return!0}return e===0?(n.setTransition(0),n.setTranslate(u),t&&(n.emit("beforeTransitionStart",e,r),n.emit("transitionEnd"))):(n.setTransition(e),n.setTranslate(u),t&&(n.emit("beforeTransitionStart",e,r),n.emit("transitionStart")),n.animating||(n.animating=!0,n.onTranslateToWrapperTransitionEnd||(n.onTranslateToWrapperTransitionEnd=function(h){!n||n.destroyed||h.target===this&&(n.wrapperEl.removeEventListener("transitionend",n.onTranslateToWrapperTransitionEnd),n.onTranslateToWrapperTransitionEnd=null,delete n.onTranslateToWrapperTransitionEnd,n.animating=!1,t&&n.emit("transitionEnd"))}),n.wrapperEl.addEventListener("transitionend",n.onTranslateToWrapperTransitionEnd))),!0}var Bt={getTranslate:Nt,setTranslate:Dt,minTranslate:kt,maxTranslate:Rt,translateTo:zt};function $t(s,e){const t=this;t.params.cssMode||(t.wrapperEl.style.transitionDuration=`${s}ms`,t.wrapperEl.style.transitionDelay=s===0?"0ms":""),t.emit("setTransition",s,e)}function We(s){let{swiper:e,runCallbacks:t,direction:i,step:r}=s;const{activeIndex:n,previousIndex:o}=e;let a=i;a||(n>o?a="next":n<o?a="prev":a="reset"),e.emit(`transition${r}`),t&&a==="reset"?e.emit(`slideResetTransition${r}`):t&&n!==o&&(e.emit(`slideChangeTransition${r}`),a==="next"?e.emit(`slideNextTransition${r}`):e.emit(`slidePrevTransition${r}`))}function Gt(s,e){s===void 0&&(s=!0);const t=this,{params:i}=t;i.cssMode||(i.autoHeight&&t.updateAutoHeight(),We({swiper:t,runCallbacks:s,direction:e,step:"Start"}))}function Ft(s,e){s===void 0&&(s=!0);const t=this,{params:i}=t;t.animating=!1,!i.cssMode&&(t.setTransition(0),We({swiper:t,runCallbacks:s,direction:e,step:"End"}))}var Vt={setTransition:$t,transitionStart:Gt,transitionEnd:Ft};function Ht(s,e,t,i,r){s===void 0&&(s=0),t===void 0&&(t=!0),typeof s=="string"&&(s=parseInt(s,10));const n=this;let o=s;o<0&&(o=0);const{params:a,snapGrid:d,slidesGrid:l,previousIndex:u,activeIndex:p,rtlTranslate:h,wrapperEl:g,enabled:m}=n;if(!m&&!i&&!r||n.destroyed||n.animating&&a.preventInteractionOnTransition)return!1;typeof e>"u"&&(e=n.params.speed);const v=Math.min(n.params.slidesPerGroupSkip,o);let b=v+Math.floor((o-v)/n.params.slidesPerGroup);b>=d.length&&(b=d.length-1);const S=-d[b];if(a.normalizeSlideIndex)for(let I=0;I<l.length;I+=1){const C=-Math.floor(S*100),L=Math.floor(l[I]*100),y=Math.floor(l[I+1]*100);typeof l[I+1]<"u"?C>=L&&C<y-(y-L)/2?o=I:C>=L&&C<y&&(o=I+1):C>=L&&(o=I)}if(n.initialized&&o!==p&&(!n.allowSlideNext&&(h?S>n.translate&&S>n.minTranslate():S<n.translate&&S<n.minTranslate())||!n.allowSlidePrev&&S>n.translate&&S>n.maxTranslate()&&(p||0)!==o))return!1;o!==(u||0)&&t&&n.emit("beforeSlideChangeStart"),n.updateProgress(S);let c;o>p?c="next":o<p?c="prev":c="reset";const f=n.virtual&&n.params.virtual.enabled;if(!(f&&r)&&(h&&-S===n.translate||!h&&S===n.translate))return n.updateActiveIndex(o),a.autoHeight&&n.updateAutoHeight(),n.updateSlidesClasses(),a.effect!=="slide"&&n.setTranslate(S),c!=="reset"&&(n.transitionStart(t,c),n.transitionEnd(t,c)),!1;if(a.cssMode){const I=n.isHorizontal(),C=h?S:-S;if(e===0)f&&(n.wrapperEl.style.scrollSnapType="none",n._immediateVirtual=!0),f&&!n._cssModeVirtualInitialSet&&n.params.initialSlide>0?(n._cssModeVirtualInitialSet=!0,requestAnimationFrame(()=>{g[I?"scrollLeft":"scrollTop"]=C})):g[I?"scrollLeft":"scrollTop"]=C,f&&requestAnimationFrame(()=>{n.wrapperEl.style.scrollSnapType="",n._immediateVirtual=!1});else{if(!n.support.smoothScroll)return ze({swiper:n,targetPosition:C,side:I?"left":"top"}),!0;g.scrollTo({[I?"left":"top"]:C,behavior:"smooth"})}return!0}const x=Ve().isSafari;return f&&!r&&x&&n.isElement&&n.virtual.update(!1,!1,o),n.setTransition(e),n.setTranslate(S),n.updateActiveIndex(o),n.updateSlidesClasses(),n.emit("beforeTransitionStart",e,i),n.transitionStart(t,c),e===0?n.transitionEnd(t,c):n.animating||(n.animating=!0,n.onSlideToWrapperTransitionEnd||(n.onSlideToWrapperTransitionEnd=function(C){!n||n.destroyed||C.target===this&&(n.wrapperEl.removeEventListener("transitionend",n.onSlideToWrapperTransitionEnd),n.onSlideToWrapperTransitionEnd=null,delete n.onSlideToWrapperTransitionEnd,n.transitionEnd(t,c))}),n.wrapperEl.addEventListener("transitionend",n.onSlideToWrapperTransitionEnd)),!0}function Wt(s,e,t,i){s===void 0&&(s=0),t===void 0&&(t=!0),typeof s=="string"&&(s=parseInt(s,10));const r=this;if(r.destroyed)return;typeof e>"u"&&(e=r.params.speed);const n=r.grid&&r.params.grid&&r.params.grid.rows>1;let o=s;if(r.params.loop)if(r.virtual&&r.params.virtual.enabled)o=o+r.virtual.slidesBefore;else{let a;if(n){const h=o*r.params.grid.rows;a=r.slides.find(g=>g.getAttribute("data-swiper-slide-index")*1===h).column}else a=r.getSlideIndexByData(o);const d=n?Math.ceil(r.slides.length/r.params.grid.rows):r.slides.length,{centeredSlides:l}=r.params;let u=r.params.slidesPerView;u==="auto"?u=r.slidesPerViewDynamic():(u=Math.ceil(parseFloat(r.params.slidesPerView,10)),l&&u%2===0&&(u=u+1));let p=d-a<u;if(l&&(p=p||a<Math.ceil(u/2)),i&&l&&r.params.slidesPerView!=="auto"&&!n&&(p=!1),p){const h=l?a<r.activeIndex?"prev":"next":a-r.activeIndex-1<r.params.slidesPerView?"next":"prev";r.loopFix({direction:h,slideTo:!0,activeSlideIndex:h==="next"?a+1:a-d+1,slideRealIndex:h==="next"?r.realIndex:void 0})}if(n){const h=o*r.params.grid.rows;o=r.slides.find(g=>g.getAttribute("data-swiper-slide-index")*1===h).column}else o=r.getSlideIndexByData(o)}return requestAnimationFrame(()=>{r.slideTo(o,e,t,i)}),r}function qt(s,e,t){e===void 0&&(e=!0);const i=this,{enabled:r,params:n,animating:o}=i;if(!r||i.destroyed)return i;typeof s>"u"&&(s=i.params.speed);let a=n.slidesPerGroup;n.slidesPerView==="auto"&&n.slidesPerGroup===1&&n.slidesPerGroupAuto&&(a=Math.max(i.slidesPerViewDynamic("current",!0),1));const d=i.activeIndex<n.slidesPerGroupSkip?1:a,l=i.virtual&&n.virtual.enabled;if(n.loop){if(o&&!l&&n.loopPreventsSliding)return!1;if(i.loopFix({direction:"next"}),i._clientLeft=i.wrapperEl.clientLeft,i.activeIndex===i.slides.length-1&&n.cssMode)return requestAnimationFrame(()=>{i.slideTo(i.activeIndex+d,s,e,t)}),!0}return n.rewind&&i.isEnd?i.slideTo(0,s,e,t):i.slideTo(i.activeIndex+d,s,e,t)}function Xt(s,e,t){e===void 0&&(e=!0);const i=this,{params:r,snapGrid:n,slidesGrid:o,rtlTranslate:a,enabled:d,animating:l}=i;if(!d||i.destroyed)return i;typeof s>"u"&&(s=i.params.speed);const u=i.virtual&&r.virtual.enabled;if(r.loop){if(l&&!u&&r.loopPreventsSliding)return!1;i.loopFix({direction:"prev"}),i._clientLeft=i.wrapperEl.clientLeft}const p=a?i.translate:-i.translate;function h(c){return c<0?-Math.floor(Math.abs(c)):Math.floor(c)}const g=h(p),m=n.map(c=>h(c)),v=r.freeMode&&r.freeMode.enabled;let b=n[m.indexOf(g)-1];if(typeof b>"u"&&(r.cssMode||v)){let c;n.forEach((f,w)=>{g>=f&&(c=w)}),typeof c<"u"&&(b=v?n[c]:n[c>0?c-1:c])}let S=0;if(typeof b<"u"&&(S=o.indexOf(b),S<0&&(S=i.activeIndex-1),r.slidesPerView==="auto"&&r.slidesPerGroup===1&&r.slidesPerGroupAuto&&(S=S-i.slidesPerViewDynamic("previous",!0)+1,S=Math.max(S,0))),r.rewind&&i.isBeginning){const c=i.params.virtual&&i.params.virtual.enabled&&i.virtual?i.virtual.slides.length-1:i.slides.length-1;return i.slideTo(c,s,e,t)}else if(r.loop&&i.activeIndex===0&&r.cssMode)return requestAnimationFrame(()=>{i.slideTo(S,s,e,t)}),!0;return i.slideTo(S,s,e,t)}function Ut(s,e,t){e===void 0&&(e=!0);const i=this;if(!i.destroyed)return typeof s>"u"&&(s=i.params.speed),i.slideTo(i.activeIndex,s,e,t)}function jt(s,e,t,i){e===void 0&&(e=!0),i===void 0&&(i=.5);const r=this;if(r.destroyed)return;typeof s>"u"&&(s=r.params.speed);let n=r.activeIndex;const o=Math.min(r.params.slidesPerGroupSkip,n),a=o+Math.floor((n-o)/r.params.slidesPerGroup),d=r.rtlTranslate?r.translate:-r.translate;if(d>=r.snapGrid[a]){const l=r.snapGrid[a],u=r.snapGrid[a+1];d-l>(u-l)*i&&(n+=r.params.slidesPerGroup)}else{const l=r.snapGrid[a-1],u=r.snapGrid[a];d-l<=(u-l)*i&&(n-=r.params.slidesPerGroup)}return n=Math.max(n,0),n=Math.min(n,r.slidesGrid.length-1),r.slideTo(n,s,e,t)}function Yt(){const s=this;if(s.destroyed)return;const{params:e,slidesEl:t}=s,i=e.slidesPerView==="auto"?s.slidesPerViewDynamic():e.slidesPerView;let r=s.getSlideIndexWhenGrid(s.clickedIndex),n;const o=s.isElement?"swiper-slide":`.${e.slideClass}`,a=s.grid&&s.params.grid&&s.params.grid.rows>1;if(e.loop){if(s.animating)return;n=parseInt(s.clickedSlide.getAttribute("data-swiper-slide-index"),10),e.centeredSlides?s.slideToLoop(n):r>(a?(s.slides.length-i)/2-(s.params.grid.rows-1):s.slides.length-i)?(s.loopFix(),r=s.getSlideIndex(j(t,`${o}[data-swiper-slide-index="${n}"]`)[0]),Re(()=>{s.slideTo(r)})):s.slideTo(r)}else s.slideTo(r)}var Kt={slideTo:Ht,slideToLoop:Wt,slideNext:qt,slidePrev:Xt,slideReset:Ut,slideToClosest:jt,slideToClickedSlide:Yt};function Jt(s,e){const t=this,{params:i,slidesEl:r}=t;if(!i.loop||t.virtual&&t.params.virtual.enabled)return;const n=()=>{j(r,`.${i.slideClass}, swiper-slide`).forEach((g,m)=>{g.setAttribute("data-swiper-slide-index",m)})},o=()=>{const h=j(r,`.${i.slideBlankClass}`);h.forEach(g=>{g.remove()}),h.length>0&&(t.recalcSlides(),t.updateSlides())},a=t.grid&&i.grid&&i.grid.rows>1;i.loopAddBlankSlides&&(i.slidesPerGroup>1||a)&&o();const d=i.slidesPerGroup*(a?i.grid.rows:1),l=t.slides.length%d!==0,u=a&&t.slides.length%i.grid.rows!==0,p=h=>{for(let g=0;g<h;g+=1){const m=t.isElement?ee("swiper-slide",[i.slideBlankClass]):ee("div",[i.slideClass,i.slideBlankClass]);t.slidesEl.append(m)}};if(l){if(i.loopAddBlankSlides){const h=d-t.slides.length%d;p(h),t.recalcSlides(),t.updateSlides()}else ae("Swiper Loop Warning: The number of slides is not even to slidesPerGroup, loop mode may not function properly. You need to add more slides (or make duplicates, or empty slides)");n()}else if(u){if(i.loopAddBlankSlides){const h=i.grid.rows-t.slides.length%i.grid.rows;p(h),t.recalcSlides(),t.updateSlides()}else ae("Swiper Loop Warning: The number of slides is not even to grid.rows, loop mode may not function properly. You need to add more slides (or make duplicates, or empty slides)");n()}else n();t.loopFix({slideRealIndex:s,direction:i.centeredSlides?void 0:"next",initial:e})}function Zt(s){let{slideRealIndex:e,slideTo:t=!0,direction:i,setTranslate:r,activeSlideIndex:n,initial:o,byController:a,byMousewheel:d}=s===void 0?{}:s;const l=this;if(!l.params.loop)return;l.emit("beforeLoopFix");const{slides:u,allowSlidePrev:p,allowSlideNext:h,slidesEl:g,params:m}=l,{centeredSlides:v,initialSlide:b}=m;if(l.allowSlidePrev=!0,l.allowSlideNext=!0,l.virtual&&m.virtual.enabled){t&&(!m.centeredSlides&&l.snapIndex===0?l.slideTo(l.virtual.slides.length,0,!1,!0):m.centeredSlides&&l.snapIndex<m.slidesPerView?l.slideTo(l.virtual.slides.length+l.snapIndex,0,!1,!0):l.snapIndex===l.snapGrid.length-1&&l.slideTo(l.virtual.slidesBefore,0,!1,!0)),l.allowSlidePrev=p,l.allowSlideNext=h,l.emit("loopFix");return}let S=m.slidesPerView;S==="auto"?S=l.slidesPerViewDynamic():(S=Math.ceil(parseFloat(m.slidesPerView,10)),v&&S%2===0&&(S=S+1));const c=m.slidesPerGroupAuto?S:m.slidesPerGroup;let f=v?Math.max(c,Math.ceil(S/2)):c;f%c!==0&&(f+=c-f%c),f+=m.loopAdditionalSlides,l.loopedSlides=f;const w=l.grid&&m.grid&&m.grid.rows>1;u.length<S+f||l.params.effect==="cards"&&u.length<S+f*2?ae("Swiper Loop Warning: The number of slides is not enough for loop mode, it will be disabled or not function properly. You need to add more slides (or make duplicates) or lower the values of slidesPerView and slidesPerGroup parameters"):w&&m.grid.fill==="row"&&ae("Swiper Loop Warning: Loop mode is not compatible with grid.fill = `row`");const T=[],x=[],I=w?Math.ceil(u.length/m.grid.rows):u.length,C=o&&I-b<S&&!v;let L=C?b:l.activeIndex;typeof n>"u"?n=l.getSlideIndex(u.find(A=>A.classList.contains(m.slideActiveClass))):L=n;const y=i==="next"||!i,E=i==="prev"||!i;let P=0,O=0;const D=(w?u[n].column:n)+(v&&typeof r>"u"?-S/2+.5:0);if(D<f){P=Math.max(f-D,c);for(let A=0;A<f-D;A+=1){const k=A-Math.floor(A/I)*I;if(w){const M=I-k-1;for(let z=u.length-1;z>=0;z-=1)u[z].column===M&&T.push(z)}else T.push(I-k-1)}}else if(D+S>I-f){O=Math.max(D-(I-f*2),c),C&&(O=Math.max(O,S-I+b+1));for(let A=0;A<O;A+=1){const k=A-Math.floor(A/I)*I;w?u.forEach((M,z)=>{M.column===k&&x.push(z)}):x.push(k)}}if(l.__preventObserver__=!0,requestAnimationFrame(()=>{l.__preventObserver__=!1}),l.params.effect==="cards"&&u.length<S+f*2&&(x.includes(n)&&x.splice(x.indexOf(n),1),T.includes(n)&&T.splice(T.indexOf(n),1)),E&&T.forEach(A=>{u[A].swiperLoopMoveDOM=!0,g.prepend(u[A]),u[A].swiperLoopMoveDOM=!1}),y&&x.forEach(A=>{u[A].swiperLoopMoveDOM=!0,g.append(u[A]),u[A].swiperLoopMoveDOM=!1}),l.recalcSlides(),m.slidesPerView==="auto"?l.updateSlides():w&&(T.length>0&&E||x.length>0&&y)&&l.slides.forEach((A,k)=>{l.grid.updateSlide(k,A,l.slides)}),m.watchSlidesProgress&&l.updateSlidesOffset(),t){if(T.length>0&&E){if(typeof e>"u"){const A=l.slidesGrid[L],M=l.slidesGrid[L+P]-A;d?l.setTranslate(l.translate-M):(l.slideTo(L+Math.ceil(P),0,!1,!0),r&&(l.touchEventsData.startTranslate=l.touchEventsData.startTranslate-M,l.touchEventsData.currentTranslate=l.touchEventsData.currentTranslate-M))}else if(r){const A=w?T.length/m.grid.rows:T.length;l.slideTo(l.activeIndex+A,0,!1,!0),l.touchEventsData.currentTranslate=l.translate}}else if(x.length>0&&y)if(typeof e>"u"){const A=l.slidesGrid[L],M=l.slidesGrid[L-O]-A;d?l.setTranslate(l.translate-M):(l.slideTo(L-O,0,!1,!0),r&&(l.touchEventsData.startTranslate=l.touchEventsData.startTranslate-M,l.touchEventsData.currentTranslate=l.touchEventsData.currentTranslate-M))}else{const A=w?x.length/m.grid.rows:x.length;l.slideTo(l.activeIndex-A,0,!1,!0)}}if(l.allowSlidePrev=p,l.allowSlideNext=h,l.controller&&l.controller.control&&!a){const A={slideRealIndex:e,direction:i,setTranslate:r,activeSlideIndex:n,byController:!0};Array.isArray(l.controller.control)?l.controller.control.forEach(k=>{!k.destroyed&&k.params.loop&&k.loopFix({...A,slideTo:k.params.slidesPerView===m.slidesPerView?t:!1})}):l.controller.control instanceof l.constructor&&l.controller.control.params.loop&&l.controller.control.loopFix({...A,slideTo:l.controller.control.params.slidesPerView===m.slidesPerView?t:!1})}l.emit("loopFix")}function Qt(){const s=this,{params:e,slidesEl:t}=s;if(!e.loop||!t||s.virtual&&s.params.virtual.enabled)return;s.recalcSlides();const i=[];s.slides.forEach(r=>{const n=typeof r.swiperSlideIndex>"u"?r.getAttribute("data-swiper-slide-index")*1:r.swiperSlideIndex;i[n]=r}),s.slides.forEach(r=>{r.removeAttribute("data-swiper-slide-index")}),i.forEach(r=>{t.append(r)}),s.recalcSlides(),s.slideTo(s.realIndex,0)}var ei={loopCreate:Jt,loopFix:Zt,loopDestroy:Qt};function ti(s){const e=this;if(!e.params.simulateTouch||e.params.watchOverflow&&e.isLocked||e.params.cssMode)return;const t=e.params.touchEventsTarget==="container"?e.el:e.wrapperEl;e.isElement&&(e.__preventObserver__=!0),t.style.cursor="move",t.style.cursor=s?"grabbing":"grab",e.isElement&&requestAnimationFrame(()=>{e.__preventObserver__=!1})}function ii(){const s=this;s.params.watchOverflow&&s.isLocked||s.params.cssMode||(s.isElement&&(s.__preventObserver__=!0),s[s.params.touchEventsTarget==="container"?"el":"wrapperEl"].style.cursor="",s.isElement&&requestAnimationFrame(()=>{s.__preventObserver__=!1}))}var si={setGrabCursor:ti,unsetGrabCursor:ii};function ri(s,e){e===void 0&&(e=this);function t(i){if(!i||i===U()||i===$())return null;i.assignedSlot&&(i=i.assignedSlot);const r=i.closest(s);return!r&&!i.getRootNode?null:r||t(i.getRootNode().host)}return t(e)}function qe(s,e,t){const i=$(),{params:r}=s,n=r.edgeSwipeDetection,o=r.edgeSwipeThreshold;return n&&(t<=o||t>=i.innerWidth-o)?n==="prevent"?(e.preventDefault(),!0):!1:!0}function ni(s){const e=this,t=U();let i=s;i.originalEvent&&(i=i.originalEvent);const r=e.touchEventsData;if(i.type==="pointerdown"){if(r.pointerId!==null&&r.pointerId!==i.pointerId)return;r.pointerId=i.pointerId}else i.type==="touchstart"&&i.targetTouches.length===1&&(r.touchId=i.targetTouches[0].identifier);if(i.type==="touchstart"){qe(e,i,i.targetTouches[0].pageX);return}const{params:n,touches:o,enabled:a}=e;if(!a||!n.simulateTouch&&i.pointerType==="mouse"||e.animating&&n.preventInteractionOnTransition)return;!e.animating&&n.cssMode&&n.loop&&e.loopFix();let d=i.target;if(n.touchEventsTarget==="wrapper"&&!ft(d,e.wrapperEl)||"which"in i&&i.which===3||"button"in i&&i.button>0||r.isTouched&&r.isMoved)return;const l=!!n.noSwipingClass&&n.noSwipingClass!=="",u=i.composedPath?i.composedPath():i.path;l&&i.target&&i.target.shadowRoot&&u&&(d=u[0]);const p=n.noSwipingSelector?n.noSwipingSelector:`.${n.noSwipingClass}`,h=!!(i.target&&i.target.shadowRoot);if(n.noSwiping&&(h?ri(p,d):d.closest(p))){e.allowClick=!0;return}if(n.swipeHandler&&!d.closest(n.swipeHandler))return;o.currentX=i.pageX,o.currentY=i.pageY;const g=o.currentX,m=o.currentY;if(!qe(e,i,g))return;Object.assign(r,{isTouched:!0,isMoved:!1,allowTouchCallbacks:!0,isScrolling:void 0,startMoving:void 0}),o.startX=g,o.startY=m,r.touchStartTime=se(),e.allowClick=!0,e.updateSize(),e.swipeDirection=void 0,n.threshold>0&&(r.allowThresholdMove=!1);let v=!0;d.matches(r.focusableElements)&&(v=!1,d.nodeName==="SELECT"&&(r.isTouched=!1)),t.activeElement&&t.activeElement.matches(r.focusableElements)&&t.activeElement!==d&&(i.pointerType==="mouse"||i.pointerType!=="mouse"&&!d.matches(r.focusableElements))&&t.activeElement.blur();const b=v&&e.allowTouchMove&&n.touchStartPreventDefault;(n.touchStartForcePreventDefault||b)&&!d.isContentEditable&&i.preventDefault(),n.freeMode&&n.freeMode.enabled&&e.freeMode&&e.animating&&!n.cssMode&&e.freeMode.onTouchStart(),e.emit("touchStart",i)}function ai(s){const e=U(),t=this,i=t.touchEventsData,{params:r,touches:n,rtlTranslate:o,enabled:a}=t;if(!a||!r.simulateTouch&&s.pointerType==="mouse")return;let d=s;if(d.originalEvent&&(d=d.originalEvent),d.type==="pointermove"&&(i.touchId!==null||d.pointerId!==i.pointerId))return;let l;if(d.type==="touchmove"){if(l=[...d.changedTouches].find(T=>T.identifier===i.touchId),!l||l.identifier!==i.touchId)return}else l=d;if(!i.isTouched){i.startMoving&&i.isScrolling&&t.emit("touchMoveOpposite",d);return}const u=l.pageX,p=l.pageY;if(d.preventedByNestedSwiper){n.startX=u,n.startY=p;return}if(!t.allowTouchMove){d.target.matches(i.focusableElements)||(t.allowClick=!1),i.isTouched&&(Object.assign(n,{startX:u,startY:p,currentX:u,currentY:p}),i.touchStartTime=se());return}if(r.touchReleaseOnEdges&&!r.loop)if(t.isVertical()){if(p<n.startY&&t.translate<=t.maxTranslate()||p>n.startY&&t.translate>=t.minTranslate()){i.isTouched=!1,i.isMoved=!1;return}}else{if(o&&(u>n.startX&&-t.translate<=t.maxTranslate()||u<n.startX&&-t.translate>=t.minTranslate()))return;if(!o&&(u<n.startX&&t.translate<=t.maxTranslate()||u>n.startX&&t.translate>=t.minTranslate()))return}if(e.activeElement&&e.activeElement.matches(i.focusableElements)&&e.activeElement!==d.target&&d.pointerType!=="mouse"&&e.activeElement.blur(),e.activeElement&&d.target===e.activeElement&&d.target.matches(i.focusableElements)){i.isMoved=!0,t.allowClick=!1;return}i.allowTouchCallbacks&&t.emit("touchMove",d),n.previousX=n.currentX,n.previousY=n.currentY,n.currentX=u,n.currentY=p;const h=n.currentX-n.startX,g=n.currentY-n.startY;if(t.params.threshold&&Math.sqrt(h**2+g**2)<t.params.threshold)return;if(typeof i.isScrolling>"u"){let T;t.isHorizontal()&&n.currentY===n.startY||t.isVertical()&&n.currentX===n.startX?i.isScrolling=!1:h*h+g*g>=25&&(T=Math.atan2(Math.abs(g),Math.abs(h))*180/Math.PI,i.isScrolling=t.isHorizontal()?T>r.touchAngle:90-T>r.touchAngle)}if(i.isScrolling&&t.emit("touchMoveOpposite",d),typeof i.startMoving>"u"&&(n.currentX!==n.startX||n.currentY!==n.startY)&&(i.startMoving=!0),i.isScrolling||d.type==="touchmove"&&i.preventTouchMoveFromPointerMove){i.isTouched=!1;return}if(!i.startMoving)return;t.allowClick=!1,!r.cssMode&&d.cancelable&&d.preventDefault(),r.touchMoveStopPropagation&&!r.nested&&d.stopPropagation();let m=t.isHorizontal()?h:g,v=t.isHorizontal()?n.currentX-n.previousX:n.currentY-n.previousY;r.oneWayMovement&&(m=Math.abs(m)*(o?1:-1),v=Math.abs(v)*(o?1:-1)),n.diff=m,m*=r.touchRatio,o&&(m=-m,v=-v);const b=t.touchesDirection;t.swipeDirection=m>0?"prev":"next",t.touchesDirection=v>0?"prev":"next";const S=t.params.loop&&!r.cssMode,c=t.touchesDirection==="next"&&t.allowSlideNext||t.touchesDirection==="prev"&&t.allowSlidePrev;if(!i.isMoved){if(S&&c&&t.loopFix({direction:t.swipeDirection}),i.startTranslate=t.getTranslate(),t.setTransition(0),t.animating){const T=new window.CustomEvent("transitionend",{bubbles:!0,cancelable:!0,detail:{bySwiperTouchMove:!0}});t.wrapperEl.dispatchEvent(T)}i.allowMomentumBounce=!1,r.grabCursor&&(t.allowSlideNext===!0||t.allowSlidePrev===!0)&&t.setGrabCursor(!0),t.emit("sliderFirstMove",d)}if(new Date().getTime(),r._loopSwapReset!==!1&&i.isMoved&&i.allowThresholdMove&&b!==t.touchesDirection&&S&&c&&Math.abs(m)>=1){Object.assign(n,{startX:u,startY:p,currentX:u,currentY:p,startTranslate:i.currentTranslate}),i.loopSwapReset=!0,i.startTranslate=i.currentTranslate;return}t.emit("sliderMove",d),i.isMoved=!0,i.currentTranslate=m+i.startTranslate;let f=!0,w=r.resistanceRatio;if(r.touchReleaseOnEdges&&(w=0),m>0?(S&&c&&i.allowThresholdMove&&i.currentTranslate>(r.centeredSlides?t.minTranslate()-t.slidesSizesGrid[t.activeIndex+1]-(r.slidesPerView!=="auto"&&t.slides.length-r.slidesPerView>=2?t.slidesSizesGrid[t.activeIndex+1]+t.params.spaceBetween:0)-t.params.spaceBetween:t.minTranslate())&&t.loopFix({direction:"prev",setTranslate:!0,activeSlideIndex:0}),i.currentTranslate>t.minTranslate()&&(f=!1,r.resistance&&(i.currentTranslate=t.minTranslate()-1+(-t.minTranslate()+i.startTranslate+m)**w))):m<0&&(S&&c&&i.allowThresholdMove&&i.currentTranslate<(r.centeredSlides?t.maxTranslate()+t.slidesSizesGrid[t.slidesSizesGrid.length-1]+t.params.spaceBetween+(r.slidesPerView!=="auto"&&t.slides.length-r.slidesPerView>=2?t.slidesSizesGrid[t.slidesSizesGrid.length-1]+t.params.spaceBetween:0):t.maxTranslate())&&t.loopFix({direction:"next",setTranslate:!0,activeSlideIndex:t.slides.length-(r.slidesPerView==="auto"?t.slidesPerViewDynamic():Math.ceil(parseFloat(r.slidesPerView,10)))}),i.currentTranslate<t.maxTranslate()&&(f=!1,r.resistance&&(i.currentTranslate=t.maxTranslate()+1-(t.maxTranslate()-i.startTranslate-m)**w))),f&&(d.preventedByNestedSwiper=!0),!t.allowSlideNext&&t.swipeDirection==="next"&&i.currentTranslate<i.startTranslate&&(i.currentTranslate=i.startTranslate),!t.allowSlidePrev&&t.swipeDirection==="prev"&&i.currentTranslate>i.startTranslate&&(i.currentTranslate=i.startTranslate),!t.allowSlidePrev&&!t.allowSlideNext&&(i.currentTranslate=i.startTranslate),r.threshold>0)if(Math.abs(m)>r.threshold||i.allowThresholdMove){if(!i.allowThresholdMove){i.allowThresholdMove=!0,n.startX=n.currentX,n.startY=n.currentY,i.currentTranslate=i.startTranslate,n.diff=t.isHorizontal()?n.currentX-n.startX:n.currentY-n.startY;return}}else{i.currentTranslate=i.startTranslate;return}!r.followFinger||r.cssMode||((r.freeMode&&r.freeMode.enabled&&t.freeMode||r.watchSlidesProgress)&&(t.updateActiveIndex(),t.updateSlidesClasses()),r.freeMode&&r.freeMode.enabled&&t.freeMode&&t.freeMode.onTouchMove(),t.updateProgress(i.currentTranslate),t.setTranslate(i.currentTranslate))}function oi(s){const e=this,t=e.touchEventsData;let i=s;i.originalEvent&&(i=i.originalEvent);let r;if(i.type==="touchend"||i.type==="touchcancel"){if(r=[...i.changedTouches].find(T=>T.identifier===t.touchId),!r||r.identifier!==t.touchId)return}else{if(t.touchId!==null||i.pointerId!==t.pointerId)return;r=i}if(["pointercancel","pointerout","pointerleave","contextmenu"].includes(i.type)&&!(["pointercancel","contextmenu"].includes(i.type)&&(e.browser.isSafari||e.browser.isWebView)))return;t.pointerId=null,t.touchId=null;const{params:o,touches:a,rtlTranslate:d,slidesGrid:l,enabled:u}=e;if(!u||!o.simulateTouch&&i.pointerType==="mouse")return;if(t.allowTouchCallbacks&&e.emit("touchEnd",i),t.allowTouchCallbacks=!1,!t.isTouched){t.isMoved&&o.grabCursor&&e.setGrabCursor(!1),t.isMoved=!1,t.startMoving=!1;return}o.grabCursor&&t.isMoved&&t.isTouched&&(e.allowSlideNext===!0||e.allowSlidePrev===!0)&&e.setGrabCursor(!1);const p=se(),h=p-t.touchStartTime;if(e.allowClick){const T=i.path||i.composedPath&&i.composedPath();e.updateClickedSlide(T&&T[0]||i.target,T),e.emit("tap click",i),h<300&&p-t.lastClickTime<300&&e.emit("doubleTap doubleClick",i)}if(t.lastClickTime=se(),Re(()=>{e.destroyed||(e.allowClick=!0)}),!t.isTouched||!t.isMoved||!e.swipeDirection||a.diff===0&&!t.loopSwapReset||t.currentTranslate===t.startTranslate&&!t.loopSwapReset){t.isTouched=!1,t.isMoved=!1,t.startMoving=!1;return}t.isTouched=!1,t.isMoved=!1,t.startMoving=!1;let g;if(o.followFinger?g=d?e.translate:-e.translate:g=-t.currentTranslate,o.cssMode)return;if(o.freeMode&&o.freeMode.enabled){e.freeMode.onTouchEnd({currentPos:g});return}const m=g>=-e.maxTranslate()&&!e.params.loop;let v=0,b=e.slidesSizesGrid[0];for(let T=0;T<l.length;T+=T<o.slidesPerGroupSkip?1:o.slidesPerGroup){const x=T<o.slidesPerGroupSkip-1?1:o.slidesPerGroup;typeof l[T+x]<"u"?(m||g>=l[T]&&g<l[T+x])&&(v=T,b=l[T+x]-l[T]):(m||g>=l[T])&&(v=T,b=l[l.length-1]-l[l.length-2])}let S=null,c=null;o.rewind&&(e.isBeginning?c=o.virtual&&o.virtual.enabled&&e.virtual?e.virtual.slides.length-1:e.slides.length-1:e.isEnd&&(S=0));const f=(g-l[v])/b,w=v<o.slidesPerGroupSkip-1?1:o.slidesPerGroup;if(h>o.longSwipesMs){if(!o.longSwipes){e.slideTo(e.activeIndex);return}e.swipeDirection==="next"&&(f>=o.longSwipesRatio?e.slideTo(o.rewind&&e.isEnd?S:v+w):e.slideTo(v)),e.swipeDirection==="prev"&&(f>1-o.longSwipesRatio?e.slideTo(v+w):c!==null&&f<0&&Math.abs(f)>o.longSwipesRatio?e.slideTo(c):e.slideTo(v))}else{if(!o.shortSwipes){e.slideTo(e.activeIndex);return}e.navigation&&(i.target===e.navigation.nextEl||i.target===e.navigation.prevEl)?i.target===e.navigation.nextEl?e.slideTo(v+w):e.slideTo(v):(e.swipeDirection==="next"&&e.slideTo(S!==null?S:v+w),e.swipeDirection==="prev"&&e.slideTo(c!==null?c:v))}}function Xe(){const s=this,{params:e,el:t}=s;if(t&&t.offsetWidth===0)return;e.breakpoints&&s.setBreakpoint();const{allowSlideNext:i,allowSlidePrev:r,snapGrid:n}=s,o=s.virtual&&s.params.virtual.enabled;s.allowSlideNext=!0,s.allowSlidePrev=!0,s.updateSize(),s.updateSlides(),s.updateSlidesClasses();const a=o&&e.loop;(e.slidesPerView==="auto"||e.slidesPerView>1)&&s.isEnd&&!s.isBeginning&&!s.params.centeredSlides&&!a?s.slideTo(s.slides.length-1,0,!1,!0):s.params.loop&&!o?s.slideToLoop(s.realIndex,0,!1,!0):s.slideTo(s.activeIndex,0,!1,!0),s.autoplay&&s.autoplay.running&&s.autoplay.paused&&(clearTimeout(s.autoplay.resizeTimeout),s.autoplay.resizeTimeout=setTimeout(()=>{s.autoplay&&s.autoplay.running&&s.autoplay.paused&&s.autoplay.resume()},500)),s.allowSlidePrev=r,s.allowSlideNext=i,s.params.watchOverflow&&n!==s.snapGrid&&s.checkOverflow()}function li(s){const e=this;e.enabled&&(e.allowClick||(e.params.preventClicks&&s.preventDefault(),e.params.preventClicksPropagation&&e.animating&&(s.stopPropagation(),s.stopImmediatePropagation())))}function di(){const s=this,{wrapperEl:e,rtlTranslate:t,enabled:i}=s;if(!i)return;s.previousTranslate=s.translate,s.isHorizontal()?s.translate=-e.scrollLeft:s.translate=-e.scrollTop,s.translate===0&&(s.translate=0),s.updateActiveIndex(),s.updateSlidesClasses();let r;const n=s.maxTranslate()-s.minTranslate();n===0?r=0:r=(s.translate-s.minTranslate())/n,r!==s.progress&&s.updateProgress(t?-s.translate:s.translate),s.emit("setTranslate",s.translate,!1)}function ci(s){const e=this;le(e,s.target),!(e.params.cssMode||e.params.slidesPerView!=="auto"&&!e.params.autoHeight)&&e.update()}function ui(){const s=this;s.documentTouchHandlerProceeded||(s.documentTouchHandlerProceeded=!0,s.params.touchReleaseOnEdges&&(s.el.style.touchAction="auto"))}const Ue=(s,e)=>{const t=U(),{params:i,el:r,wrapperEl:n,device:o}=s,a=!!i.nested,d=e==="on"?"addEventListener":"removeEventListener",l=e;!r||typeof r=="string"||(t[d]("touchstart",s.onDocumentTouchStart,{passive:!1,capture:a}),r[d]("touchstart",s.onTouchStart,{passive:!1}),r[d]("pointerdown",s.onTouchStart,{passive:!1}),t[d]("touchmove",s.onTouchMove,{passive:!1,capture:a}),t[d]("pointermove",s.onTouchMove,{passive:!1,capture:a}),t[d]("touchend",s.onTouchEnd,{passive:!0}),t[d]("pointerup",s.onTouchEnd,{passive:!0}),t[d]("pointercancel",s.onTouchEnd,{passive:!0}),t[d]("touchcancel",s.onTouchEnd,{passive:!0}),t[d]("pointerout",s.onTouchEnd,{passive:!0}),t[d]("pointerleave",s.onTouchEnd,{passive:!0}),t[d]("contextmenu",s.onTouchEnd,{passive:!0}),(i.preventClicks||i.preventClicksPropagation)&&r[d]("click",s.onClick,!0),i.cssMode&&n[d]("scroll",s.onScroll),i.updateOnWindowResize?s[l](o.ios||o.android?"resize orientationchange observerUpdate":"resize observerUpdate",Xe,!0):s[l]("observerUpdate",Xe,!0),r[d]("load",s.onLoad,{capture:!0}))};function fi(){const s=this,{params:e}=s;s.onTouchStart=ni.bind(s),s.onTouchMove=ai.bind(s),s.onTouchEnd=oi.bind(s),s.onDocumentTouchStart=ui.bind(s),e.cssMode&&(s.onScroll=di.bind(s)),s.onClick=li.bind(s),s.onLoad=ci.bind(s),Ue(s,"on")}function pi(){Ue(this,"off")}var mi={attachEvents:fi,detachEvents:pi};const je=(s,e)=>s.grid&&e.grid&&e.grid.rows>1;function hi(){const s=this,{realIndex:e,initialized:t,params:i,el:r}=s,n=i.breakpoints;if(!n||n&&Object.keys(n).length===0)return;const o=U(),a=i.breakpointsBase==="window"||!i.breakpointsBase?i.breakpointsBase:"container",d=["window","container"].includes(i.breakpointsBase)||!i.breakpointsBase?s.el:o.querySelector(i.breakpointsBase),l=s.getBreakpoint(n,a,d);if(!l||s.currentBreakpoint===l)return;const p=(l in n?n[l]:void 0)||s.originalParams,h=je(s,i),g=je(s,p),m=s.params.grabCursor,v=p.grabCursor,b=i.enabled;h&&!g?(r.classList.remove(`${i.containerModifierClass}grid`,`${i.containerModifierClass}grid-column`),s.emitContainerClasses()):!h&&g&&(r.classList.add(`${i.containerModifierClass}grid`),(p.grid.fill&&p.grid.fill==="column"||!p.grid.fill&&i.grid.fill==="column")&&r.classList.add(`${i.containerModifierClass}grid-column`),s.emitContainerClasses()),m&&!v?s.unsetGrabCursor():!m&&v&&s.setGrabCursor(),["navigation","pagination","scrollbar"].forEach(x=>{if(typeof p[x]>"u")return;const I=i[x]&&i[x].enabled,C=p[x]&&p[x].enabled;I&&!C&&s[x].disable(),!I&&C&&s[x].enable()});const S=p.direction&&p.direction!==i.direction,c=i.loop&&(p.slidesPerView!==i.slidesPerView||S),f=i.loop;S&&t&&s.changeDirection(),F(s.params,p);const w=s.params.enabled,T=s.params.loop;Object.assign(s,{allowTouchMove:s.params.allowTouchMove,allowSlideNext:s.params.allowSlideNext,allowSlidePrev:s.params.allowSlidePrev}),b&&!w?s.disable():!b&&w&&s.enable(),s.currentBreakpoint=l,s.emit("_beforeBreakpoint",p),t&&(c?(s.loopDestroy(),s.loopCreate(e),s.updateSlides()):!f&&T?(s.loopCreate(e),s.updateSlides()):f&&!T&&s.loopDestroy()),s.emit("breakpoint",p)}function gi(s,e,t){if(e===void 0&&(e="window"),!s||e==="container"&&!t)return;let i=!1;const r=$(),n=e==="window"?r.innerHeight:t.clientHeight,o=Object.keys(s).map(a=>{if(typeof a=="string"&&a.indexOf("@")===0){const d=parseFloat(a.substr(1));return{value:n*d,point:a}}return{value:a,point:a}});o.sort((a,d)=>parseInt(a.value,10)-parseInt(d.value,10));for(let a=0;a<o.length;a+=1){const{point:d,value:l}=o[a];e==="window"?r.matchMedia(`(min-width: ${l}px)`).matches&&(i=d):l<=t.clientWidth&&(i=d)}return i||"max"}var vi={setBreakpoint:hi,getBreakpoint:gi};function wi(s,e){const t=[];return s.forEach(i=>{typeof i=="object"?Object.keys(i).forEach(r=>{i[r]&&t.push(e+r)}):typeof i=="string"&&t.push(e+i)}),t}function Si(){const s=this,{classNames:e,params:t,rtl:i,el:r,device:n}=s,o=wi(["initialized",t.direction,{"free-mode":s.params.freeMode&&t.freeMode.enabled},{autoheight:t.autoHeight},{rtl:i},{grid:t.grid&&t.grid.rows>1},{"grid-column":t.grid&&t.grid.rows>1&&t.grid.fill==="column"},{android:n.android},{ios:n.ios},{"css-mode":t.cssMode},{centered:t.cssMode&&t.centeredSlides},{"watch-progress":t.watchSlidesProgress}],t.containerModifierClass);e.push(...o),r.classList.add(...e),s.emitContainerClasses()}function Ti(){const s=this,{el:e,classNames:t}=s;!e||typeof e=="string"||(e.classList.remove(...t),s.emitContainerClasses())}var yi={addClasses:Si,removeClasses:Ti};function Ei(){const s=this,{isLocked:e,params:t}=s,{slidesOffsetBefore:i}=t;if(i){const r=s.slides.length-1,n=s.slidesGrid[r]+s.slidesSizesGrid[r]+i*2;s.isLocked=s.size>n}else s.isLocked=s.snapGrid.length===1;t.allowSlideNext===!0&&(s.allowSlideNext=!s.isLocked),t.allowSlidePrev===!0&&(s.allowSlidePrev=!s.isLocked),e&&e!==s.isLocked&&(s.isEnd=!1),e!==s.isLocked&&s.emit(s.isLocked?"lock":"unlock")}var bi={checkOverflow:Ei},Ye={init:!0,direction:"horizontal",oneWayMovement:!1,swiperElementNodeName:"SWIPER-CONTAINER",touchEventsTarget:"wrapper",initialSlide:0,speed:300,cssMode:!1,updateOnWindowResize:!0,resizeObserver:!0,nested:!1,createElements:!1,eventsPrefix:"swiper",enabled:!0,focusableElements:"input, select, option, textarea, button, video, label",width:null,height:null,preventInteractionOnTransition:!1,userAgent:null,url:null,edgeSwipeDetection:!1,edgeSwipeThreshold:20,autoHeight:!1,setWrapperSize:!1,virtualTranslate:!1,effect:"slide",breakpoints:void 0,breakpointsBase:"window",spaceBetween:0,slidesPerView:1,slidesPerGroup:1,slidesPerGroupSkip:0,slidesPerGroupAuto:!1,centeredSlides:!1,centeredSlidesBounds:!1,slidesOffsetBefore:0,slidesOffsetAfter:0,normalizeSlideIndex:!0,centerInsufficientSlides:!1,watchOverflow:!0,roundLengths:!1,touchRatio:1,touchAngle:45,simulateTouch:!0,shortSwipes:!0,longSwipes:!0,longSwipesRatio:.5,longSwipesMs:300,followFinger:!0,allowTouchMove:!0,threshold:5,touchMoveStopPropagation:!1,touchStartPreventDefault:!0,touchStartForcePreventDefault:!1,touchReleaseOnEdges:!1,uniqueNavElements:!0,resistance:!0,resistanceRatio:.85,watchSlidesProgress:!1,grabCursor:!1,preventClicks:!0,preventClicksPropagation:!0,slideToClickedSlide:!1,loop:!1,loopAddBlankSlides:!0,loopAdditionalSlides:0,loopPreventsSliding:!0,rewind:!1,allowSlidePrev:!0,allowSlideNext:!0,swipeHandler:null,noSwiping:!0,noSwipingClass:"swiper-no-swiping",noSwipingSelector:null,passiveListeners:!0,maxBackfaceHiddenSlides:10,containerModifierClass:"swiper-",slideClass:"swiper-slide",slideBlankClass:"swiper-slide-blank",slideActiveClass:"swiper-slide-active",slideVisibleClass:"swiper-slide-visible",slideFullyVisibleClass:"swiper-slide-fully-visible",slideNextClass:"swiper-slide-next",slidePrevClass:"swiper-slide-prev",wrapperClass:"swiper-wrapper",lazyPreloaderClass:"swiper-lazy-preloader",lazyPreloadPrevNext:0,runCallbacksOnInit:!0,_emitClasses:!1};function Ii(s,e){return function(i){i===void 0&&(i={});const r=Object.keys(i)[0],n=i[r];if(typeof n!="object"||n===null){F(e,i);return}if(s[r]===!0&&(s[r]={enabled:!0}),r==="navigation"&&s[r]&&s[r].enabled&&!s[r].prevEl&&!s[r].nextEl&&(s[r].auto=!0),["pagination","scrollbar"].indexOf(r)>=0&&s[r]&&s[r].enabled&&!s[r].el&&(s[r].auto=!0),!(r in s&&"enabled"in n)){F(e,i);return}typeof s[r]=="object"&&!("enabled"in s[r])&&(s[r].enabled=!0),s[r]||(s[r]={enabled:!1}),F(e,i)}}const xe={eventsEmitter:yt,update:_t,translate:Bt,transition:Vt,slide:Kt,loop:ei,grabCursor:si,events:mi,breakpoints:vi,checkOverflow:bi,classes:yi},Ce={};class V{constructor(){let e,t;for(var i=arguments.length,r=new Array(i),n=0;n<i;n++)r[n]=arguments[n];r.length===1&&r[0].constructor&&Object.prototype.toString.call(r[0]).slice(8,-1)==="Object"?t=r[0]:[e,t]=r,t||(t={}),t=F({},t),e&&!t.el&&(t.el=e);const o=U();if(t.el&&typeof t.el=="string"&&o.querySelectorAll(t.el).length>1){const u=[];return o.querySelectorAll(t.el).forEach(p=>{const h=F({},t,{el:p});u.push(new V(h))}),u}const a=this;a.__swiper__=!0,a.support=Ge(),a.device=Fe({userAgent:t.userAgent}),a.browser=Ve(),a.eventsListeners={},a.eventsAnyListeners=[],a.modules=[...a.__modules__],t.modules&&Array.isArray(t.modules)&&a.modules.push(...t.modules);const d={};a.modules.forEach(u=>{u({params:t,swiper:a,extendParams:Ii(t,d),on:a.on.bind(a),once:a.once.bind(a),off:a.off.bind(a),emit:a.emit.bind(a)})});const l=F({},Ye,d);return a.params=F({},l,Ce,t),a.originalParams=F({},a.params),a.passedParams=F({},t),a.params&&a.params.on&&Object.keys(a.params.on).forEach(u=>{a.on(u,a.params.on[u])}),a.params&&a.params.onAny&&a.onAny(a.params.onAny),Object.assign(a,{enabled:a.params.enabled,el:e,classNames:[],slides:[],slidesGrid:[],snapGrid:[],slidesSizesGrid:[],isHorizontal(){return a.params.direction==="horizontal"},isVertical(){return a.params.direction==="vertical"},activeIndex:0,realIndex:0,isBeginning:!0,isEnd:!1,translate:0,previousTranslate:0,progress:0,velocity:0,animating:!1,cssOverflowAdjustment(){return Math.trunc(this.translate/2**23)*2**23},allowSlideNext:a.params.allowSlideNext,allowSlidePrev:a.params.allowSlidePrev,touchEventsData:{isTouched:void 0,isMoved:void 0,allowTouchCallbacks:void 0,touchStartTime:void 0,isScrolling:void 0,currentTranslate:void 0,startTranslate:void 0,allowThresholdMove:void 0,focusableElements:a.params.focusableElements,lastClickTime:0,clickTimeout:void 0,velocities:[],allowMomentumBounce:void 0,startMoving:void 0,pointerId:null,touchId:null},allowClick:!0,allowTouchMove:a.params.allowTouchMove,touches:{startX:0,startY:0,currentX:0,currentY:0,diff:0},imagesToLoad:[],imagesLoaded:0}),a.emit("_swiper"),a.params.init&&a.init(),a}getDirectionLabel(e){return this.isHorizontal()?e:{width:"height","margin-top":"margin-left","margin-bottom ":"margin-right","margin-left":"margin-top","margin-right":"margin-bottom","padding-left":"padding-top","padding-right":"padding-bottom",marginRight:"marginBottom"}[e]}getSlideIndex(e){const{slidesEl:t,params:i}=this,r=j(t,`.${i.slideClass}, swiper-slide`),n=oe(r[0]);return oe(e)-n}getSlideIndexByData(e){return this.getSlideIndex(this.slides.find(t=>t.getAttribute("data-swiper-slide-index")*1===e))}getSlideIndexWhenGrid(e){return this.grid&&this.params.grid&&this.params.grid.rows>1&&(this.params.grid.fill==="column"?e=Math.floor(e/this.params.grid.rows):this.params.grid.fill==="row"&&(e=e%Math.ceil(this.slides.length/this.params.grid.rows))),e}recalcSlides(){const e=this,{slidesEl:t,params:i}=e;e.slides=j(t,`.${i.slideClass}, swiper-slide`)}enable(){const e=this;e.enabled||(e.enabled=!0,e.params.grabCursor&&e.setGrabCursor(),e.emit("enable"))}disable(){const e=this;e.enabled&&(e.enabled=!1,e.params.grabCursor&&e.unsetGrabCursor(),e.emit("disable"))}setProgress(e,t){const i=this;e=Math.min(Math.max(e,0),1);const r=i.minTranslate(),o=(i.maxTranslate()-r)*e+r;i.translateTo(o,typeof t>"u"?0:t),i.updateActiveIndex(),i.updateSlidesClasses()}emitContainerClasses(){const e=this;if(!e.params._emitClasses||!e.el)return;const t=e.el.className.split(" ").filter(i=>i.indexOf("swiper")===0||i.indexOf(e.params.containerModifierClass)===0);e.emit("_containerClasses",t.join(" "))}getSlideClasses(e){const t=this;return t.destroyed?"":e.className.split(" ").filter(i=>i.indexOf("swiper-slide")===0||i.indexOf(t.params.slideClass)===0).join(" ")}emitSlidesClasses(){const e=this;if(!e.params._emitClasses||!e.el)return;const t=[];e.slides.forEach(i=>{const r=e.getSlideClasses(i);t.push({slideEl:i,classNames:r}),e.emit("_slideClass",i,r)}),e.emit("_slideClasses",t)}slidesPerViewDynamic(e,t){e===void 0&&(e="current"),t===void 0&&(t=!1);const i=this,{params:r,slides:n,slidesGrid:o,slidesSizesGrid:a,size:d,activeIndex:l}=i;let u=1;if(typeof r.slidesPerView=="number")return r.slidesPerView;if(r.centeredSlides){let p=n[l]?Math.ceil(n[l].swiperSlideSize):0,h;for(let g=l+1;g<n.length;g+=1)n[g]&&!h&&(p+=Math.ceil(n[g].swiperSlideSize),u+=1,p>d&&(h=!0));for(let g=l-1;g>=0;g-=1)n[g]&&!h&&(p+=n[g].swiperSlideSize,u+=1,p>d&&(h=!0))}else if(e==="current")for(let p=l+1;p<n.length;p+=1)(t?o[p]+a[p]-o[l]<d:o[p]-o[l]<d)&&(u+=1);else for(let p=l-1;p>=0;p-=1)o[l]-o[p]<d&&(u+=1);return u}update(){const e=this;if(!e||e.destroyed)return;const{snapGrid:t,params:i}=e;i.breakpoints&&e.setBreakpoint(),[...e.el.querySelectorAll('[loading="lazy"]')].forEach(o=>{o.complete&&le(e,o)}),e.updateSize(),e.updateSlides(),e.updateProgress(),e.updateSlidesClasses();function r(){const o=e.rtlTranslate?e.translate*-1:e.translate,a=Math.min(Math.max(o,e.maxTranslate()),e.minTranslate());e.setTranslate(a),e.updateActiveIndex(),e.updateSlidesClasses()}let n;if(i.freeMode&&i.freeMode.enabled&&!i.cssMode)r(),i.autoHeight&&e.updateAutoHeight();else{if((i.slidesPerView==="auto"||i.slidesPerView>1)&&e.isEnd&&!i.centeredSlides){const o=e.virtual&&i.virtual.enabled?e.virtual.slides:e.slides;n=e.slideTo(o.length-1,0,!1,!0)}else n=e.slideTo(e.activeIndex,0,!1,!0);n||r()}i.watchOverflow&&t!==e.snapGrid&&e.checkOverflow(),e.emit("update")}changeDirection(e,t){t===void 0&&(t=!0);const i=this,r=i.params.direction;return e||(e=r==="horizontal"?"vertical":"horizontal"),e===r||e!=="horizontal"&&e!=="vertical"||(i.el.classList.remove(`${i.params.containerModifierClass}${r}`),i.el.classList.add(`${i.params.containerModifierClass}${e}`),i.emitContainerClasses(),i.params.direction=e,i.slides.forEach(n=>{e==="vertical"?n.style.width="":n.style.height=""}),i.emit("changeDirection"),t&&i.update()),i}changeLanguageDirection(e){const t=this;t.rtl&&e==="rtl"||!t.rtl&&e==="ltr"||(t.rtl=e==="rtl",t.rtlTranslate=t.params.direction==="horizontal"&&t.rtl,t.rtl?(t.el.classList.add(`${t.params.containerModifierClass}rtl`),t.el.dir="rtl"):(t.el.classList.remove(`${t.params.containerModifierClass}rtl`),t.el.dir="ltr"),t.update())}mount(e){const t=this;if(t.mounted)return!0;let i=e||t.params.el;if(typeof i=="string"&&(i=document.querySelector(i)),!i)return!1;i.swiper=t,i.parentNode&&i.parentNode.host&&i.parentNode.host.nodeName===t.params.swiperElementNodeName.toUpperCase()&&(t.isElement=!0);const r=()=>`.${(t.params.wrapperClass||"").trim().split(" ").join(".")}`;let o=i&&i.shadowRoot&&i.shadowRoot.querySelector?i.shadowRoot.querySelector(r()):j(i,r())[0];return!o&&t.params.createElements&&(o=ee("div",t.params.wrapperClass),i.append(o),j(i,`.${t.params.slideClass}`).forEach(a=>{o.append(a)})),Object.assign(t,{el:i,wrapperEl:o,slidesEl:t.isElement&&!i.parentNode.host.slideSlots?i.parentNode.host:o,hostEl:t.isElement?i.parentNode.host:i,mounted:!0,rtl:i.dir.toLowerCase()==="rtl"||K(i,"direction")==="rtl",rtlTranslate:t.params.direction==="horizontal"&&(i.dir.toLowerCase()==="rtl"||K(i,"direction")==="rtl"),wrongRTL:K(o,"display")==="-webkit-box"}),!0}init(e){const t=this;if(t.initialized||t.mount(e)===!1)return t;t.emit("beforeInit"),t.params.breakpoints&&t.setBreakpoint(),t.addClasses(),t.updateSize(),t.updateSlides(),t.params.watchOverflow&&t.checkOverflow(),t.params.grabCursor&&t.enabled&&t.setGrabCursor(),t.params.loop&&t.virtual&&t.params.virtual.enabled?t.slideTo(t.params.initialSlide+t.virtual.slidesBefore,0,t.params.runCallbacksOnInit,!1,!0):t.slideTo(t.params.initialSlide,0,t.params.runCallbacksOnInit,!1,!0),t.params.loop&&t.loopCreate(void 0,!0),t.attachEvents();const r=[...t.el.querySelectorAll('[loading="lazy"]')];return t.isElement&&r.push(...t.hostEl.querySelectorAll('[loading="lazy"]')),r.forEach(n=>{n.complete?le(t,n):n.addEventListener("load",o=>{le(t,o.target)})}),Ie(t),t.initialized=!0,Ie(t),t.emit("init"),t.emit("afterInit"),t}destroy(e,t){e===void 0&&(e=!0),t===void 0&&(t=!0);const i=this,{params:r,el:n,wrapperEl:o,slides:a}=i;return typeof i.params>"u"||i.destroyed||(i.emit("beforeDestroy"),i.initialized=!1,i.detachEvents(),r.loop&&i.loopDestroy(),t&&(i.removeClasses(),n&&typeof n!="string"&&n.removeAttribute("style"),o&&o.removeAttribute("style"),a&&a.length&&a.forEach(d=>{d.classList.remove(r.slideVisibleClass,r.slideFullyVisibleClass,r.slideActiveClass,r.slideNextClass,r.slidePrevClass),d.removeAttribute("style"),d.removeAttribute("data-swiper-slide-index")})),i.emit("destroy"),Object.keys(i.eventsListeners).forEach(d=>{i.off(d)}),e!==!1&&(i.el&&typeof i.el!="string"&&(i.el.swiper=null),ot(i)),i.destroyed=!0),null}static extendDefaults(e){F(Ce,e)}static get extendedDefaults(){return Ce}static get defaults(){return Ye}static installModule(e){V.prototype.__modules__||(V.prototype.__modules__=[]);const t=V.prototype.__modules__;typeof e=="function"&&t.indexOf(e)<0&&t.push(e)}static use(e){return Array.isArray(e)?(e.forEach(t=>V.installModule(t)),V):(V.installModule(e),V)}}Object.keys(xe).forEach(s=>{Object.keys(xe[s]).forEach(e=>{V.prototype[e]=xe[s][e]})}),V.use([St,Tt]);function Ke(s,e,t,i){return s.params.createElements&&Object.keys(i).forEach(r=>{if(!t[r]&&t.auto===!0){let n=j(s.el,`.${i[r]}`)[0];n||(n=ee("div",i[r]),n.className=i[r],s.el.append(n)),t[r]=n,e[r]=n}}),t}function xi(s){let{swiper:e,extendParams:t,on:i,emit:r}=s;t({navigation:{nextEl:null,prevEl:null,hideOnClick:!1,disabledClass:"swiper-button-disabled",hiddenClass:"swiper-button-hidden",lockClass:"swiper-button-lock",navigationDisabledClass:"swiper-navigation-disabled"}}),e.navigation={nextEl:null,prevEl:null};function n(m){let v;return m&&typeof m=="string"&&e.isElement&&(v=e.el.querySelector(m)||e.hostEl.querySelector(m),v)?v:(m&&(typeof m=="string"&&(v=[...document.querySelectorAll(m)]),e.params.uniqueNavElements&&typeof m=="string"&&v&&v.length>1&&e.el.querySelectorAll(m).length===1?v=e.el.querySelector(m):v&&v.length===1&&(v=v[0])),m&&!v?m:v)}function o(m,v){const b=e.params.navigation;m=B(m),m.forEach(S=>{S&&(S.classList[v?"add":"remove"](...b.disabledClass.split(" ")),S.tagName==="BUTTON"&&(S.disabled=v),e.params.watchOverflow&&e.enabled&&S.classList[e.isLocked?"add":"remove"](b.lockClass))})}function a(){const{nextEl:m,prevEl:v}=e.navigation;if(e.params.loop){o(v,!1),o(m,!1);return}o(v,e.isBeginning&&!e.params.rewind),o(m,e.isEnd&&!e.params.rewind)}function d(m){m.preventDefault(),!(e.isBeginning&&!e.params.loop&&!e.params.rewind)&&(e.slidePrev(),r("navigationPrev"))}function l(m){m.preventDefault(),!(e.isEnd&&!e.params.loop&&!e.params.rewind)&&(e.slideNext(),r("navigationNext"))}function u(){const m=e.params.navigation;if(e.params.navigation=Ke(e,e.originalParams.navigation,e.params.navigation,{nextEl:"swiper-button-next",prevEl:"swiper-button-prev"}),!(m.nextEl||m.prevEl))return;let v=n(m.nextEl),b=n(m.prevEl);Object.assign(e.navigation,{nextEl:v,prevEl:b}),v=B(v),b=B(b);const S=(c,f)=>{c&&c.addEventListener("click",f==="next"?l:d),!e.enabled&&c&&c.classList.add(...m.lockClass.split(" "))};v.forEach(c=>S(c,"next")),b.forEach(c=>S(c,"prev"))}function p(){let{nextEl:m,prevEl:v}=e.navigation;m=B(m),v=B(v);const b=(S,c)=>{S.removeEventListener("click",c==="next"?l:d),S.classList.remove(...e.params.navigation.disabledClass.split(" "))};m.forEach(S=>b(S,"next")),v.forEach(S=>b(S,"prev"))}i("init",()=>{e.params.navigation.enabled===!1?g():(u(),a())}),i("toEdge fromEdge lock unlock",()=>{a()}),i("destroy",()=>{p()}),i("enable disable",()=>{let{nextEl:m,prevEl:v}=e.navigation;if(m=B(m),v=B(v),e.enabled){a();return}[...m,...v].filter(b=>!!b).forEach(b=>b.classList.add(e.params.navigation.lockClass))}),i("click",(m,v)=>{let{nextEl:b,prevEl:S}=e.navigation;b=B(b),S=B(S);const c=v.target;let f=S.includes(c)||b.includes(c);if(e.isElement&&!f){const w=v.path||v.composedPath&&v.composedPath();w&&(f=w.find(T=>b.includes(T)||S.includes(T)))}if(e.params.navigation.hideOnClick&&!f){if(e.pagination&&e.params.pagination&&e.params.pagination.clickable&&(e.pagination.el===c||e.pagination.el.contains(c)))return;let w;b.length?w=b[0].classList.contains(e.params.navigation.hiddenClass):S.length&&(w=S[0].classList.contains(e.params.navigation.hiddenClass)),r(w===!0?"navigationShow":"navigationHide"),[...b,...S].filter(T=>!!T).forEach(T=>T.classList.toggle(e.params.navigation.hiddenClass))}});const h=()=>{e.el.classList.remove(...e.params.navigation.navigationDisabledClass.split(" ")),u(),a()},g=()=>{e.el.classList.add(...e.params.navigation.navigationDisabledClass.split(" ")),p()};Object.assign(e.navigation,{enable:h,disable:g,update:a,init:u,destroy:p})}function te(s){return s===void 0&&(s=""),`.${s.trim().replace(/([\.:!+\/()[\]])/g,"\\$1").replace(/ /g,".")}`}function Ci(s){let{swiper:e,extendParams:t,on:i,emit:r}=s;const n="swiper-pagination";t({pagination:{el:null,bulletElement:"span",clickable:!1,hideOnClick:!1,renderBullet:null,renderProgressbar:null,renderFraction:null,renderCustom:null,progressbarOpposite:!1,type:"bullets",dynamicBullets:!1,dynamicMainBullets:1,formatFractionCurrent:c=>c,formatFractionTotal:c=>c,bulletClass:`${n}-bullet`,bulletActiveClass:`${n}-bullet-active`,modifierClass:`${n}-`,currentClass:`${n}-current`,totalClass:`${n}-total`,hiddenClass:`${n}-hidden`,progressbarFillClass:`${n}-progressbar-fill`,progressbarOppositeClass:`${n}-progressbar-opposite`,clickableClass:`${n}-clickable`,lockClass:`${n}-lock`,horizontalClass:`${n}-horizontal`,verticalClass:`${n}-vertical`,paginationDisabledClass:`${n}-disabled`}}),e.pagination={el:null,bullets:[]};let o,a=0;function d(){return!e.params.pagination.el||!e.pagination.el||Array.isArray(e.pagination.el)&&e.pagination.el.length===0}function l(c,f){const{bulletActiveClass:w}=e.params.pagination;c&&(c=c[`${f==="prev"?"previous":"next"}ElementSibling`],c&&(c.classList.add(`${w}-${f}`),c=c[`${f==="prev"?"previous":"next"}ElementSibling`],c&&c.classList.add(`${w}-${f}-${f}`)))}function u(c,f,w){if(c=c%w,f=f%w,f===c+1)return"next";if(f===c-1)return"previous"}function p(c){const f=c.target.closest(te(e.params.pagination.bulletClass));if(!f)return;c.preventDefault();const w=oe(f)*e.params.slidesPerGroup;if(e.params.loop){if(e.realIndex===w)return;const T=u(e.realIndex,w,e.slides.length);T==="next"?e.slideNext():T==="previous"?e.slidePrev():e.slideToLoop(w)}else e.slideTo(w)}function h(){const c=e.rtl,f=e.params.pagination;if(d())return;let w=e.pagination.el;w=B(w);let T,x;const I=e.virtual&&e.params.virtual.enabled?e.virtual.slides.length:e.slides.length,C=e.params.loop?Math.ceil(I/e.params.slidesPerGroup):e.snapGrid.length;if(e.params.loop?(x=e.previousRealIndex||0,T=e.params.slidesPerGroup>1?Math.floor(e.realIndex/e.params.slidesPerGroup):e.realIndex):typeof e.snapIndex<"u"?(T=e.snapIndex,x=e.previousSnapIndex):(x=e.previousIndex||0,T=e.activeIndex||0),f.type==="bullets"&&e.pagination.bullets&&e.pagination.bullets.length>0){const L=e.pagination.bullets;let y,E,P;if(f.dynamicBullets&&(o=we(L[0],e.isHorizontal()?"width":"height"),w.forEach(O=>{O.style[e.isHorizontal()?"width":"height"]=`${o*(f.dynamicMainBullets+4)}px`}),f.dynamicMainBullets>1&&x!==void 0&&(a+=T-(x||0),a>f.dynamicMainBullets-1?a=f.dynamicMainBullets-1:a<0&&(a=0)),y=Math.max(T-a,0),E=y+(Math.min(L.length,f.dynamicMainBullets)-1),P=(E+y)/2),L.forEach(O=>{const _=[...["","-next","-next-next","-prev","-prev-prev","-main"].map(D=>`${f.bulletActiveClass}${D}`)].map(D=>typeof D=="string"&&D.includes(" ")?D.split(" "):D).flat();O.classList.remove(..._)}),w.length>1)L.forEach(O=>{const _=oe(O);_===T?O.classList.add(...f.bulletActiveClass.split(" ")):e.isElement&&O.setAttribute("part","bullet"),f.dynamicBullets&&(_>=y&&_<=E&&O.classList.add(...`${f.bulletActiveClass}-main`.split(" ")),_===y&&l(O,"prev"),_===E&&l(O,"next"))});else{const O=L[T];if(O&&O.classList.add(...f.bulletActiveClass.split(" ")),e.isElement&&L.forEach((_,D)=>{_.setAttribute("part",D===T?"bullet-active":"bullet")}),f.dynamicBullets){const _=L[y],D=L[E];for(let A=y;A<=E;A+=1)L[A]&&L[A].classList.add(...`${f.bulletActiveClass}-main`.split(" "));l(_,"prev"),l(D,"next")}}if(f.dynamicBullets){const O=Math.min(L.length,f.dynamicMainBullets+4),_=(o*O-o)/2-P*o,D=c?"right":"left";L.forEach(A=>{A.style[e.isHorizontal()?D:"top"]=`${_}px`})}}w.forEach((L,y)=>{if(f.type==="fraction"&&(L.querySelectorAll(te(f.currentClass)).forEach(E=>{E.textContent=f.formatFractionCurrent(T+1)}),L.querySelectorAll(te(f.totalClass)).forEach(E=>{E.textContent=f.formatFractionTotal(C)})),f.type==="progressbar"){let E;f.progressbarOpposite?E=e.isHorizontal()?"vertical":"horizontal":E=e.isHorizontal()?"horizontal":"vertical";const P=(T+1)/C;let O=1,_=1;E==="horizontal"?O=P:_=P,L.querySelectorAll(te(f.progressbarFillClass)).forEach(D=>{D.style.transform=`translate3d(0,0,0) scaleX(${O}) scaleY(${_})`,D.style.transitionDuration=`${e.params.speed}ms`})}f.type==="custom"&&f.renderCustom?($e(L,f.renderCustom(e,T+1,C)),y===0&&r("paginationRender",L)):(y===0&&r("paginationRender",L),r("paginationUpdate",L)),e.params.watchOverflow&&e.enabled&&L.classList[e.isLocked?"add":"remove"](f.lockClass)})}function g(){const c=e.params.pagination;if(d())return;const f=e.virtual&&e.params.virtual.enabled?e.virtual.slides.length:e.grid&&e.params.grid.rows>1?e.slides.length/Math.ceil(e.params.grid.rows):e.slides.length;let w=e.pagination.el;w=B(w);let T="";if(c.type==="bullets"){let x=e.params.loop?Math.ceil(f/e.params.slidesPerGroup):e.snapGrid.length;e.params.freeMode&&e.params.freeMode.enabled&&x>f&&(x=f);for(let I=0;I<x;I+=1)c.renderBullet?T+=c.renderBullet.call(e,I,c.bulletClass):T+=`<${c.bulletElement} ${e.isElement?'part="bullet"':""} class="${c.bulletClass}"></${c.bulletElement}>`}c.type==="fraction"&&(c.renderFraction?T=c.renderFraction.call(e,c.currentClass,c.totalClass):T=`<span class="${c.currentClass}"></span> / <span class="${c.totalClass}"></span>`),c.type==="progressbar"&&(c.renderProgressbar?T=c.renderProgressbar.call(e,c.progressbarFillClass):T=`<span class="${c.progressbarFillClass}"></span>`),e.pagination.bullets=[],w.forEach(x=>{c.type!=="custom"&&$e(x,T||""),c.type==="bullets"&&e.pagination.bullets.push(...x.querySelectorAll(te(c.bulletClass)))}),c.type!=="custom"&&r("paginationRender",w[0])}function m(){e.params.pagination=Ke(e,e.originalParams.pagination,e.params.pagination,{el:"swiper-pagination"});const c=e.params.pagination;if(!c.el)return;let f;typeof c.el=="string"&&e.isElement&&(f=e.el.querySelector(c.el)),!f&&typeof c.el=="string"&&(f=[...document.querySelectorAll(c.el)]),f||(f=c.el),!(!f||f.length===0)&&(e.params.uniqueNavElements&&typeof c.el=="string"&&Array.isArray(f)&&f.length>1&&(f=[...e.el.querySelectorAll(c.el)],f.length>1&&(f=f.find(w=>Be(w,".swiper")[0]===e.el))),Array.isArray(f)&&f.length===1&&(f=f[0]),Object.assign(e.pagination,{el:f}),f=B(f),f.forEach(w=>{c.type==="bullets"&&c.clickable&&w.classList.add(...(c.clickableClass||"").split(" ")),w.classList.add(c.modifierClass+c.type),w.classList.add(e.isHorizontal()?c.horizontalClass:c.verticalClass),c.type==="bullets"&&c.dynamicBullets&&(w.classList.add(`${c.modifierClass}${c.type}-dynamic`),a=0,c.dynamicMainBullets<1&&(c.dynamicMainBullets=1)),c.type==="progressbar"&&c.progressbarOpposite&&w.classList.add(c.progressbarOppositeClass),c.clickable&&w.addEventListener("click",p),e.enabled||w.classList.add(c.lockClass)}))}function v(){const c=e.params.pagination;if(d())return;let f=e.pagination.el;f&&(f=B(f),f.forEach(w=>{w.classList.remove(c.hiddenClass),w.classList.remove(c.modifierClass+c.type),w.classList.remove(e.isHorizontal()?c.horizontalClass:c.verticalClass),c.clickable&&(w.classList.remove(...(c.clickableClass||"").split(" ")),w.removeEventListener("click",p))})),e.pagination.bullets&&e.pagination.bullets.forEach(w=>w.classList.remove(...c.bulletActiveClass.split(" ")))}i("changeDirection",()=>{if(!e.pagination||!e.pagination.el)return;const c=e.params.pagination;let{el:f}=e.pagination;f=B(f),f.forEach(w=>{w.classList.remove(c.horizontalClass,c.verticalClass),w.classList.add(e.isHorizontal()?c.horizontalClass:c.verticalClass)})}),i("init",()=>{e.params.pagination.enabled===!1?S():(m(),g(),h())}),i("activeIndexChange",()=>{typeof e.snapIndex>"u"&&h()}),i("snapIndexChange",()=>{h()}),i("snapGridLengthChange",()=>{g(),h()}),i("destroy",()=>{v()}),i("enable disable",()=>{let{el:c}=e.pagination;c&&(c=B(c),c.forEach(f=>f.classList[e.enabled?"remove":"add"](e.params.pagination.lockClass)))}),i("lock unlock",()=>{h()}),i("click",(c,f)=>{const w=f.target,T=B(e.pagination.el);if(e.params.pagination.el&&e.params.pagination.hideOnClick&&T&&T.length>0&&!w.classList.contains(e.params.pagination.bulletClass)){if(e.navigation&&(e.navigation.nextEl&&w===e.navigation.nextEl||e.navigation.prevEl&&w===e.navigation.prevEl))return;const x=T[0].classList.contains(e.params.pagination.hiddenClass);r(x===!0?"paginationShow":"paginationHide"),T.forEach(I=>I.classList.toggle(e.params.pagination.hiddenClass))}});const b=()=>{e.el.classList.remove(e.params.pagination.paginationDisabledClass);let{el:c}=e.pagination;c&&(c=B(c),c.forEach(f=>f.classList.remove(e.params.pagination.paginationDisabledClass))),m(),g(),h()},S=()=>{e.el.classList.add(e.params.pagination.paginationDisabledClass);let{el:c}=e.pagination;c&&(c=B(c),c.forEach(f=>f.classList.add(e.params.pagination.paginationDisabledClass))),v()};Object.assign(e.pagination,{enable:b,disable:S,render:g,update:h,init:m,destroy:v})}function Je(s){let{swiper:e,extendParams:t,on:i,emit:r,params:n}=s;e.autoplay={running:!1,paused:!1,timeLeft:0},t({autoplay:{enabled:!1,delay:3e3,waitForTransition:!0,disableOnInteraction:!1,stopOnLastSlide:!1,reverseDirection:!1,pauseOnMouseEnter:!1}});let o,a,d=n&&n.autoplay?n.autoplay.delay:3e3,l=n&&n.autoplay?n.autoplay.delay:3e3,u,p=new Date().getTime(),h,g,m,v,b,S,c;function f(M){!e||e.destroyed||!e.wrapperEl||M.target===e.wrapperEl&&(e.wrapperEl.removeEventListener("transitionend",f),!(c||M.detail&&M.detail.bySwiperTouchMove)&&y())}const w=()=>{if(e.destroyed||!e.autoplay.running)return;e.autoplay.paused?h=!0:h&&(l=u,h=!1);const M=e.autoplay.paused?u:p+l-new Date().getTime();e.autoplay.timeLeft=M,r("autoplayTimeLeft",M,M/d),a=requestAnimationFrame(()=>{w()})},T=()=>{let M;return e.virtual&&e.params.virtual.enabled?M=e.slides.find(G=>G.classList.contains("swiper-slide-active")):M=e.slides[e.activeIndex],M?parseInt(M.getAttribute("data-swiper-autoplay"),10):void 0},x=M=>{if(e.destroyed||!e.autoplay.running)return;cancelAnimationFrame(a),w();let z=typeof M>"u"?e.params.autoplay.delay:M;d=e.params.autoplay.delay,l=e.params.autoplay.delay;const G=T();!Number.isNaN(G)&&G>0&&typeof M>"u"&&(z=G,d=G,l=G),u=z;const Z=e.params.speed,me=()=>{!e||e.destroyed||(e.params.autoplay.reverseDirection?!e.isBeginning||e.params.loop||e.params.rewind?(e.slidePrev(Z,!0,!0),r("autoplay")):e.params.autoplay.stopOnLastSlide||(e.slideTo(e.slides.length-1,Z,!0,!0),r("autoplay")):!e.isEnd||e.params.loop||e.params.rewind?(e.slideNext(Z,!0,!0),r("autoplay")):e.params.autoplay.stopOnLastSlide||(e.slideTo(0,Z,!0,!0),r("autoplay")),e.params.cssMode&&(p=new Date().getTime(),requestAnimationFrame(()=>{x()})))};return z>0?(clearTimeout(o),o=setTimeout(()=>{me()},z)):requestAnimationFrame(()=>{me()}),z},I=()=>{p=new Date().getTime(),e.autoplay.running=!0,x(),r("autoplayStart")},C=()=>{e.autoplay.running=!1,clearTimeout(o),cancelAnimationFrame(a),r("autoplayStop")},L=(M,z)=>{if(e.destroyed||!e.autoplay.running)return;clearTimeout(o),M||(S=!0);const G=()=>{r("autoplayPause"),e.params.autoplay.waitForTransition?e.wrapperEl.addEventListener("transitionend",f):y()};if(e.autoplay.paused=!0,z){b&&(u=e.params.autoplay.delay),b=!1,G();return}u=(u||e.params.autoplay.delay)-(new Date().getTime()-p),!(e.isEnd&&u<0&&!e.params.loop)&&(u<0&&(u=0),G())},y=()=>{e.isEnd&&u<0&&!e.params.loop||e.destroyed||!e.autoplay.running||(p=new Date().getTime(),S?(S=!1,x(u)):x(),e.autoplay.paused=!1,r("autoplayResume"))},E=()=>{if(e.destroyed||!e.autoplay.running)return;const M=U();M.visibilityState==="hidden"&&(S=!0,L(!0)),M.visibilityState==="visible"&&y()},P=M=>{M.pointerType==="mouse"&&(S=!0,c=!0,!(e.animating||e.autoplay.paused)&&L(!0))},O=M=>{M.pointerType==="mouse"&&(c=!1,e.autoplay.paused&&y())},_=()=>{e.params.autoplay.pauseOnMouseEnter&&(e.el.addEventListener("pointerenter",P),e.el.addEventListener("pointerleave",O))},D=()=>{e.el&&typeof e.el!="string"&&(e.el.removeEventListener("pointerenter",P),e.el.removeEventListener("pointerleave",O))},A=()=>{U().addEventListener("visibilitychange",E)},k=()=>{U().removeEventListener("visibilitychange",E)};i("init",()=>{e.params.autoplay.enabled&&(_(),A(),I())}),i("destroy",()=>{D(),k(),e.autoplay.running&&C()}),i("_freeModeStaticRelease",()=>{(m||S)&&y()}),i("_freeModeNoMomentumRelease",()=>{e.params.autoplay.disableOnInteraction?C():L(!0,!0)}),i("beforeTransitionStart",(M,z,G)=>{e.destroyed||!e.autoplay.running||(G||!e.params.autoplay.disableOnInteraction?L(!0,!0):C())}),i("sliderFirstMove",()=>{if(!(e.destroyed||!e.autoplay.running)){if(e.params.autoplay.disableOnInteraction){C();return}g=!0,m=!1,S=!1,v=setTimeout(()=>{S=!0,m=!0,L(!0)},200)}}),i("touchEnd",()=>{if(!(e.destroyed||!e.autoplay.running||!g)){if(clearTimeout(v),clearTimeout(o),e.params.autoplay.disableOnInteraction){m=!1,g=!1;return}m&&e.params.cssMode&&y(),m=!1,g=!1}}),i("slideChange",()=>{e.destroyed||!e.autoplay.running||(b=!0)}),Object.assign(e.autoplay,{start:I,stop:C,pause:L,resume:y})}function Mi(s){const{effect:e,swiper:t,on:i,setTranslate:r,setTransition:n,overwriteParams:o,perspective:a,recreateShadows:d,getEffectParams:l}=s;i("beforeInit",()=>{if(t.params.effect!==e)return;t.classNames.push(`${t.params.containerModifierClass}${e}`),a&&a()&&t.classNames.push(`${t.params.containerModifierClass}3d`);const p=o?o():{};Object.assign(t.params,p),Object.assign(t.originalParams,p)}),i("setTranslate _virtualUpdated",()=>{t.params.effect===e&&r()}),i("setTransition",(p,h)=>{t.params.effect===e&&n(h)}),i("transitionEnd",()=>{if(t.params.effect===e&&d){if(!l||!l().slideShadows)return;t.slides.forEach(p=>{p.querySelectorAll(".swiper-slide-shadow-top, .swiper-slide-shadow-right, .swiper-slide-shadow-bottom, .swiper-slide-shadow-left").forEach(h=>h.remove())}),d()}});let u;i("virtualUpdate",()=>{t.params.effect===e&&(t.slides.length||(u=!0),requestAnimationFrame(()=>{u&&t.slides&&t.slides.length&&(r(),u=!1)}))})}function Li(s,e){const t=ve(e);return t!==e&&(t.style.backfaceVisibility="hidden",t.style["-webkit-backface-visibility"]="hidden"),t}function Ze(s,e,t){const i=`swiper-slide-shadow${t?`-${t}`:""}${` swiper-slide-shadow-${s}`}`,r=ve(e);let n=r.querySelector(`.${i.split(" ").join(".")}`);return n||(n=ee("div",i.split(" ")),r.append(n)),n}function Pi(s){let{swiper:e,extendParams:t,on:i}=s;t({coverflowEffect:{rotate:50,stretch:0,depth:100,scale:1,modifier:1,slideShadows:!0}}),Mi({effect:"coverflow",swiper:e,on:i,setTranslate:()=>{const{width:o,height:a,slides:d,slidesSizesGrid:l}=e,u=e.params.coverflowEffect,p=e.isHorizontal(),h=e.translate,g=p?-h+o/2:-h+a/2,m=p?u.rotate:-u.rotate,v=u.depth,b=ht(e);for(let S=0,c=d.length;S<c;S+=1){const f=d[S],w=l[S],T=f.swiperSlideOffset,x=(g-T-w/2)/w,I=typeof u.modifier=="function"?u.modifier(x):x*u.modifier;let C=p?m*I:0,L=p?0:m*I,y=-v*Math.abs(I),E=u.stretch;typeof E=="string"&&E.indexOf("%")!==-1&&(E=parseFloat(u.stretch)/100*w);let P=p?0:E*I,O=p?E*I:0,_=1-(1-u.scale)*Math.abs(I);Math.abs(O)<.001&&(O=0),Math.abs(P)<.001&&(P=0),Math.abs(y)<.001&&(y=0),Math.abs(C)<.001&&(C=0),Math.abs(L)<.001&&(L=0),Math.abs(_)<.001&&(_=0);const D=`translate3d(${O}px,${P}px,${y}px)  rotateX(${b(L)}deg) rotateY(${b(C)}deg) scale(${_})`,A=Li(u,f);if(A.style.transform=D,f.style.zIndex=-Math.abs(Math.round(I))+1,u.slideShadows){let k=p?f.querySelector(".swiper-slide-shadow-left"):f.querySelector(".swiper-slide-shadow-top"),M=p?f.querySelector(".swiper-slide-shadow-right"):f.querySelector(".swiper-slide-shadow-bottom");k||(k=Ze("coverflow",f,p?"left":"top")),M||(M=Ze("coverflow",f,p?"right":"bottom")),k&&(k.style.opacity=I>0?I:0),M&&(M.style.opacity=-I>0?-I:0)}}},setTransition:o=>{e.slides.map(d=>ve(d)).forEach(d=>{d.style.transitionDuration=`${o}ms`,d.querySelectorAll(".swiper-slide-shadow-top, .swiper-slide-shadow-right, .swiper-slide-shadow-bottom, .swiper-slide-shadow-left").forEach(l=>{l.style.transitionDuration=`${o}ms`})})},perspective:()=>!0,overwriteParams:()=>({watchSlidesProgress:!0})})}const Qe={USER_AGENT_SUBSTR_START:0,USER_AGENT_SUBSTR_LENGTH:4},de={MOBILE:{SPACE_BETWEEN:90,SLIDES_PER_VIEW:2},DESKTOP:{SPACE_BETWEEN:10,SLIDES_PER_VIEW:7}},H={MAX_ERROR_LOG_ENTRIES:50,SLIDE_NUMBER_MIN:1,SLIDE_NUMBER_MAX:30,TRANSITION_TIME_MIN:1e3,TRANSITION_TIME_MAX:3e4,CONFIG_MAX_SLIDES_MIN:1,CONFIG_MAX_SLIDES_MAX:50},Me={SCREEN_WIDTH_MULTIPLIER:2,SCREEN_WIDTH_OFFSET:50,CONTAINER_MARGIN_MULTIPLIER:.254},q={SLIDE_INCREMENT:1,INITIAL_SLIDE_INDEX:1,VALIDATION_ERRORS_EMPTY:0},X={EMPTY_LENGTH:0,FIRST_INDEX:0,NOT_FOUND_INDEX:-1,NEXT_ITEM_OFFSET:1,LAST_ITEM_OFFSET:-1},et={INDENT_SIZE:2},Le={CHECK_INTERVAL:10,DATA_CHECK_INTERVAL:100,DEFAULT_TRANSITION_TIME:5e3},tt={SWIPER_AD_CONTAINER_ID:"swiperAdContainer",HEADER_ICON_ID:"wusong8899Client1HeaderIcon"},Ai={AD_SWIPER:"adSwiper"},Pe={SWIPER_PAGINATION_EL:".swiper-pagination",SWIPER_BUTTON_NEXT_EL:".swiper-button-next",SWIPER_BUTTON_PREV_EL:".swiper-button-prev"},ce={ID:"wusong8899-client1-header-adv",TRANSLATION_PREFIX:"wusong8899-client1",MAX_SLIDES:30,HEADER_ICON_URL:"https://ex.cc/assets/files/date/test.png"},W=(s,e={},t="")=>{const i=document.createElement(s);for(const[r,n]of Object.entries(e))r==="className"?i.className=n:r==="style"?i.setAttribute("style",n):i.setAttribute(r,n);return t&&(i.innerHTML=t),i},it=s=>document.getElementById(s),ie=(s,e=document)=>{try{if(!e||!s)throw new Error("Invalid selector or parent");return e.querySelector(s)}catch{return document.querySelector("")}},Ae=(s,e=document)=>{try{return!e||!s?document.querySelectorAll(""):e.querySelectorAll(s)}catch{return document.querySelectorAll("")}},Oe=(s,e)=>{if(!(!s||!e))for(const[t,i]of Object.entries(e))try{s.style.setProperty(t,i)}catch{}},Y=(s,e)=>{try{s&&e&&s.appendChild(e)}catch{}},_e=(s,e)=>{try{s&&e&&s.firstChild&&s.firstChild.before(e)}catch{}},ue=s=>{try{s&&s.parentNode&&s.parentNode.removeChild(s)}catch{}},fe={cached:!1,isMobile:!1},pe=()=>{if(fe.cached)return fe.isMobile;let s=!1;const e=navigator.userAgent||navigator.vendor||globalThis.opera,t=/(android|bb\d+|meego).+mobile|avantgo|bada\/|blackberry|blazer|compal|elaine|fennec|hiptop|iemobile|ip(hone|od)|iris|kindle|lge |maemo|midp|mmp|mobile.+firefox|netfront|opera m(ob|in)i|palm( os)?|phone|p(ixi|re)\/|plucker|pocket|psp|series(4|6)0|symbian|treo|up\.(browser|link)|vodafone|wap|windows ce|xda|xiino/i,i=/1207|6310|6590|3gso|4thp|50[1-6]i|770s|802s|a wa|abac|ac(er|oo|s-)|ai(ko|rn)|al(av|ca|co)|amoi|an(ex|ny|yw)|aptu|ar(ch|go)|as(te|us)|attw|au(di|-m|r |s )|avan|be(ck|ll|nq)|bi(lb|rd)|bl(ac|az)|br(e|v)w|bumb|bw-(n|u)|c55\/|capi|ccwa|cdm-|cell|chtm|cldc|cmd-|co(mp|nd)|craw|da(it|ll|ng)|dbte|dc-s|devi|dica|dmob|do(c|p)o|ds(12|-d)|el(49|ai)|em(l2|ul)|er(ic|k0)|esl8|ez([4-7]0|os|wa|ze)|fetc|fly(-|_)|g1 u|g560|gene|gf-5|g-mo|go(\.w|od)|gr(ad|un)|haie|hcit|hd-(m|p|t)|hei-|hi(pt|ta)|hp( i|ip)|hs-c|ht(c(-| |_|a|g|p|s|t)|tp)|hu(aw|tc)|i-(20|go|ma)|i230|iac( |-|\/)|ibro|idea|ig01|ikom|im1k|inno|ipaq|iris|ja(t|v)a|jbro|jemu|jigs|kddi|keji|kgt( |\/)|klon|kpt |kwc-|kyo(c|k)|le(no|xi)|lg( g|\/(k|l|u)|50|54|-[a-w])|libw|lynx|m1-w|m3ga|m50\/|ma(te|ui|xo)|mc(01|21|ca)|m-cr|me(rc|ri)|mi(o8|oa|ts)|mmef|mo(01|02|bi|de|do|t(-| |o|v)|zz)|mt(50|p1|v )|mwbp|mywa|n10[0-2]|n20[2-3]|n30(0|2)|n50(0|2|5)|n7(0(0|1)|10)|ne((c|m)-|on|tf|wf|wg|wt)|nok(6|i)|nzph|o2im|op(ti|wv)|oran|owg1|p800|pan(a|d|t)|pdxg|pg(13|-([1-8]|c))|phil|pire|pl(ay|uc)|pn-2|po(ck|rt|se)|prox|psio|pt-g|qa-a|qc(07|12|21|32|60|-[2-7]|i-)|qtek|r380|r600|raks|rim9|ro(ve|zo)|s55\/|sa(ge|ma|mm|ms|ny|va)|sc(01|h-|oo|p-)|sdk\/|se(c(-|0|1)|47|mc|nd|ri)|sgh-|shar|sie(-|m)|sk-0|sl(45|id)|sm(al|ar|b3|it|t5)|so(ft|ny)|sp(01|h-|v-|v )|sy(01|mb)|t2(18|50)|t6(00|10|18)|ta(gt|lk)|tcl-|tdg-|tel(i|m)|tim-|t-mo|to(pl|sh)|ts(70|m-|m3|m5)|tx-9|up(\.b|g1|si)|utst|v400|v750|veri|vi(rg|te)|vk(40|5[0-3]|-v)|vm40|voda|vulc|vx(52|53|60|61|70|80|81|83|85|98)|w3c(-| )|webc|whit|wi(g |nc|nw)|wmlb|wonu|x700|yas-|your|zeto|zte-/i,r=e.substr(Qe.USER_AGENT_SUBSTR_START,Qe.USER_AGENT_SUBSTR_LENGTH);return(t.test(e)||i.test(r))&&(s=!0),fe.isMobile=s,fe.cached=!0,s},Oi=()=>pe()?{spaceBetween:de.MOBILE.SPACE_BETWEEN,slidesPerView:de.MOBILE.SLIDES_PER_VIEW}:{spaceBetween:de.DESKTOP.SPACE_BETWEEN,slidesPerView:de.DESKTOP.SLIDES_PER_VIEW},N={env:"production",app:{extensionId:ce.ID,translationPrefix:ce.TRANSLATION_PREFIX},slider:{maxSlides:ce.MAX_SLIDES,defaultTransitionTime:Le.DEFAULT_TRANSITION_TIME,checkTime:Le.CHECK_INTERVAL,dataCheckInterval:Le.DATA_CHECK_INTERVAL,dom:{containerId:tt.SWIPER_AD_CONTAINER_ID,swiperClass:Ai.AD_SWIPER},swiper:{spaceBetween:30,effect:"coverflow",centeredSlides:!0,slidesPerView:2,coverflowEffect:{rotate:0,depth:100,modifier:1,slideShadows:!0,stretch:0},pagination:{el:Pe.SWIPER_PAGINATION_EL,type:"bullets"},navigation:{nextEl:Pe.SWIPER_BUTTON_NEXT_EL,prevEl:Pe.SWIPER_BUTTON_PREV_EL}}},ui:{headerIconId:tt.HEADER_ICON_ID,headerIconUrl:ce.HEADER_ICON_URL},data:{apiResources:{}}};class _i{constructor(){this.maxSlides=N.slider.maxSlides,this.checkTime=N.slider.checkTime}getForumAttribute(e){try{const t=R&&R.forum,i=t&&t.attribute;return typeof i=="function"?i.call(t,e):void 0}catch{return}}attachAdvertiseHeader(e){try{this.destroy();const t=this.createContainer(),i=this.createSwiperElement(t),r=this.createSwiperWrapper(i);this.populateSlides(r),this.createPagination(i),this.createNavigation(i),this.container=t,this.appendToDOM(t),setTimeout(()=>{this.initializeSwiper(this.getTransitionTime())},this.checkTime)}catch{}}removeExistingNavigation(){const e=ie(`#${N.slider.dom.containerId}`);e&&ue(e);const t=Ae(".item-nav");for(const i of t)ue(i)}createContainer(){this.removeExistingNavigation();const e=W("div",{id:N.slider.dom.containerId,className:"adContainer"});return this.applyMobileStyles(e),e}applyMobileStyles(e){if(pe()){const i=globalThis.innerWidth*Me.SCREEN_WIDTH_MULTIPLIER-Me.SCREEN_WIDTH_OFFSET;Oe(e,{width:`${i}px`,"margin-left":`${-(i*Me.CONTAINER_MARGIN_MULTIPLIER)}px`})}}createSwiperElement(e){const t=W("div",{className:`swiper ${N.slider.dom.swiperClass}`});return Y(e,t),t}createSwiperWrapper(e){const t=W("div",{className:"swiper-wrapper"});return Y(e,t),t}getTransitionTime(){const e=this.getForumAttribute("Client1HeaderAdvTransitionTime");return e?Number.parseInt(String(e),10):N.slider.defaultTransitionTime}populateSlides(e){for(let t=q.INITIAL_SLIDE_INDEX;t<=this.maxSlides;t+=q.SLIDE_INCREMENT){const i=this.getForumAttribute(`Client1HeaderAdvImage${t}`),r=this.getForumAttribute(`Client1HeaderAdvLink${t}`);if(i){const n=this.createSlide(String(i),String(r||""));Y(e,n)}}}createSlide(e,t){const i=W("div",{className:"swiper-slide"});let r="";return t&&(r=`window.location.href="${t}"`),i.innerHTML=`<img onclick='${r}' src='${e}' />`,i}createPagination(e){const t=W("div",{className:"swiper-pagination"});Y(e,t)}createNavigation(e){const t=W("div",{className:"swiper-button-prev"}),i=W("div",{className:"swiper-button-next"});Y(e,t),Y(e,i)}appendToDOM(e){const t=ie("#content .container");t&&_e(t,e)}initializeSwiper(e){try{this.swiper=new V(`.${N.slider.dom.swiperClass}`,{autoplay:{delay:e},spaceBetween:N.slider.swiper.spaceBetween,effect:N.slider.swiper.effect,centeredSlides:N.slider.swiper.centeredSlides,slidesPerView:N.slider.swiper.slidesPerView,coverflowEffect:{rotate:N.slider.swiper.coverflowEffect.rotate,depth:N.slider.swiper.coverflowEffect.depth,modifier:N.slider.swiper.coverflowEffect.modifier,slideShadows:N.slider.swiper.coverflowEffect.slideShadows,stretch:N.slider.swiper.coverflowEffect.stretch},pagination:{el:N.slider.swiper.pagination.el,type:N.slider.swiper.pagination.type},navigation:{nextEl:N.slider.swiper.navigation.nextEl,prevEl:N.slider.swiper.navigation.prevEl},modules:[Pi,xi,Ci,Je]})}catch{}}destroy(){this.swiper&&(this.swiper.destroy(!0,!0),delete this.swiper),this.container&&(ue(this.container),delete this.container)}}class Ni{changeCategoryLayout(){try{if(it("swiperTagContainer"))return;const e=Ae(".TagTile");e.length>X.EMPTY_LENGTH?this.processTagTiles(e):this.waitForTagTilesAndProcess()}catch{}}waitForTagTilesAndProcess(){let i=0;const r=()=>{i+=X.NEXT_ITEM_OFFSET;const n=Ae(".TagTile");n.length>X.EMPTY_LENGTH?this.processTagTiles(n):i<10&&setTimeout(r,200)};r()}processTagTiles(e){try{const t=this.createTagSwiperContainer();if(!t)return;const i=this.createTagSwiper(t);if(!i)return;const r=this.createTagSwiperWrapper(i);if(!r)return;this.populateTagSlides(r,e),this.appendTagContainer(t),this.addTagSwiperContent(t),this.removeOriginalTagTiles(),this.setupMobileStyles(),this.initializeTagSwiper(),this.notifyTagsLayoutChanged()}catch{}}createTagSwiperContainer(){const e=W("div",{className:"swiperTagContainer",id:"swiperTagContainer"}),t=W("div",{className:"TagTextOuterContainer"});return Y(e,t),e}createTagSwiper(e){const t=e.querySelector(".TagTextOuterContainer"),i=W("div",{className:"swiper tagSwiper"});return t&&Y(t,i),i}createTagSwiperWrapper(e){const t=W("div",{className:"swiper-wrapper",id:"swiperTagWrapper"});return Y(e,t),t}populateTagSlides(e,t){const i=pe();for(const r of t){const n=r,o=this.extractTagData(n);if(o){const a=this.createTagSlide(o,i);Y(e,a)}}}extractTagData(e){const t=e.querySelector("a"),i=e.querySelector(".TagTile-name"),r=e.querySelector(".TagTile-description");if(!t||!i)return;const n=this.getTagBackgroundImage(t.href,e),o=globalThis.getComputedStyle(e),a=n||o.background;let d="",l="";return r&&(d=r.textContent||"",l=globalThis.getComputedStyle(r).color),{url:t.href,background:a,name:i.textContent||"",nameColor:globalThis.getComputedStyle(i).color,description:d,descColor:l}}getTagBackgroundImage(e,t){try{const r=new URL(e,globalThis.location.origin).pathname.split("/").filter(Boolean),n=r.indexOf("t"),o=r.indexOf("tags");let a="";if(n!==X.NOT_FOUND_INDEX&&r[n+X.NEXT_ITEM_OFFSET]?a=r[n+X.NEXT_ITEM_OFFSET]:o!==X.NOT_FOUND_INDEX&&r[o+X.NEXT_ITEM_OFFSET]?a=r[o+X.NEXT_ITEM_OFFSET]:r.length>X.EMPTY_LENGTH&&(a=r[r.length+X.LAST_ITEM_OFFSET]),!a)return;const d=this.getTagBackgroundUrlBySlug(a);return d?`url(${d})`:void 0}catch{const i=t.style.background;return i&&i.includes("url(")?i:void 0}}getTagBackgroundUrlBySlug(e){try{const i=R.store.all("tags").find(n=>{const o=n;let a="";return typeof o.slug=="function"?a=o.slug():o.attribute&&typeof o.attribute=="function"&&(a=o.attribute("slug")),a===e});if(!i)return;const r=i;if(r.attribute&&typeof r.attribute=="function"){const n=r.attribute("wusong8899BackgroundURL");if(n)return n}return}catch{return}}createTagSlide(e,t){const i=W("div",{className:"swiper-slide swiper-slide-tag"});let r="swiper-slide-tag-inner";t&&(r="swiper-slide-tag-inner-mobile");const n=`background:${e.background};background-size: cover;background-position: center;background-repeat: no-repeat;`,o=this.hasBackgroundImage(e.background);let a="";return o||(a=`
            <div style='font-weight:bold;font-size:14px;color:${e.nameColor}'>
                ${e.name}
            </div>
        `),i.innerHTML=`
            <a href='${e.url}'>
                <div class='${r}' style='${n}'>
                    ${a}
                </div>
            </a>
        `,i}hasBackgroundImage(e){return e?e.includes("url(")&&!e.includes("url()"):!1}appendTagContainer(e){const t=ie("#content .container .TagsPage-content");t&&_e(t,e)}addTagSwiperContent(e){const t=e.querySelector(".TagTextOuterContainer");if(t){const i=W("div",{className:"TagTextContainer"},"<div class='TagTextIcon'></div>中文玩家社区资讯");_e(t,i);const r=this.createSocialButtonsHTML();t.insertAdjacentHTML("beforeend",r)}}createSocialButtonsHTML(){const e="wusong8899-client1-header-adv",i=[{urlKey:`${e}.SocialKickUrl`,iconKey:`${e}.SocialKickIcon`,defaultIcon:""},{urlKey:`${e}.SocialFacebookUrl`,iconKey:`${e}.SocialFacebookIcon`,defaultIcon:""},{urlKey:`${e}.SocialTwitterUrl`,iconKey:`${e}.SocialTwitterIcon`,defaultIcon:""},{urlKey:`${e}.SocialYouTubeUrl`,iconKey:`${e}.SocialYouTubeIcon`,defaultIcon:""},{urlKey:`${e}.SocialInstagramUrl`,iconKey:`${e}.SocialInstagramIcon`,defaultIcon:""}].map((r,n)=>{const o=R.forum.attribute(r.urlKey)||"",a=R.forum.attribute(r.iconKey)||r.defaultIcon;if(!o.trim()||!a.trim())return"";let d="";return n>X.FIRST_INDEX&&(d="margin-left: 20px;"),`<img onClick="window.open('${o}', '_blank')" style="width: 32px;${d}" src="${a}">`}).filter(r=>r!=="").join("");return i?`
            <div style="text-align:center;padding-top: 10px;">
                <button class="Button Button--primary" type="button" style="font-weight: normal !important; color:#ffa000; background: #1a1d2e !important;border-radius: 2rem !important;">
                    <div style="margin-top: 5px;" class="Button-label">
                        ${i}
                    </div>
                </button>
            </div>
        `:""}removeOriginalTagTiles(){const e=ie(".TagTiles");e&&ue(e)}setupMobileStyles(){if(pe()){const e=it("app"),t=ie(".App-content");e&&Oe(e,{"overflow-x":"hidden"}),t&&Oe(t,{"min-height":"auto",background:""})}}initializeTagSwiper(){try{const e=Oi(),t=3e3,i=new V(".tagSwiper",{loop:!0,spaceBetween:e.spaceBetween,slidesPerView:e.slidesPerView,autoplay:{delay:t,disableOnInteraction:!1},modules:[Je]})}catch{}}notifyTagsLayoutChanged(){try{const e=new CustomEvent("tagsLayoutChanged",{detail:{extensionId:"wusong8899-client1-header-adv",layoutType:"swiper"}});document.dispatchEvent(e)}catch{}}}class J{constructor(){this.config=new Map,this.typedConfig=N,this.loadDefaultConfig()}static getInstance(){return J.instance||(J.instance=new J),J.instance}loadDefaultConfig(){this.config.set("maxSlides",N.slider.maxSlides),this.config.set("defaultTransitionTime",N.slider.defaultTransitionTime),this.config.set("checkTime",N.slider.checkTime),this.config.set("dataCheckInterval",N.slider.dataCheckInterval)}get(e,t){return this.config.get(e)??t}set(e,t){this.config.set(e,t)}getForumAttribute(e){try{const t=R&&R.forum,i=t&&t.attribute;return typeof i=="function"?i.call(t,e):void 0}catch{return}}getTransitionTime(){const e=this.getForumAttribute("Client1HeaderAdvTransitionTime");return e?Number.parseInt(String(e),10):this.get("defaultTransitionTime")}getSlideImage(e){return this.getForumAttribute(`Client1HeaderAdvImage${e}`)||""}getSlideLink(e){return this.getForumAttribute(`Client1HeaderAdvLink${e}`)||""}getAllSlides(){const e=[],t=this.get("maxSlides");for(let i=q.INITIAL_SLIDE_INDEX;i<=t;i+=q.SLIDE_INCREMENT){const r=this.getSlideImage(i),n=this.getSlideLink(i);r&&e.push({slideNumber:i,image:r,link:n||"#"})}return e}getHeaderIconUrl(){return String(this.getForumAttribute("Client1HeaderAdvHeaderIconUrl")||"")}getSocialMediaUrl(e){return String(this.getForumAttribute(`wusong8899-client1-header-adv.Social${e}Url`)||"")}getSocialMediaIcon(e){return String(this.getForumAttribute(`wusong8899-client1-header-adv.Social${e}Icon`)||"")}getTranslation(e,t={}){const i=this.getTranslationKey(e),r=R&&R.translator;if(r&&r.trans){const n=r.trans(i,t);return rt(n)}return e}getTranslationKey(e){return`${N.app.translationPrefix}.${e}`}isUserLoggedIn(){return!!(R&&R.session&&R.session.user)}getCurrentRoute(){const e=R&&R.current;return e&&e.get&&e.get("routeName")||""}isTagsPage(){const e=this.getCurrentRoute(),t=globalThis.location&&globalThis.location.pathname||"";return e==="tags"||e==="tag"||t.includes("/tags")||t==="/"||document.querySelector(".TagsPage")!==null}isDiscussionPage(){return this.getCurrentRoute()==="discussion"}isUserPage(){return this.getCurrentRoute()==="user"}isSlideValid(e){const t=this.getSlideImage(e);return typeof t=="string"&&t.trim().length>q.VALIDATION_ERRORS_EMPTY}getValidSlideCount(){const e=this.get("maxSlides");let t=q.VALIDATION_ERRORS_EMPTY;for(let i=q.INITIAL_SLIDE_INDEX;i<=e;i+=q.SLIDE_INCREMENT)this.isSlideValid(i)&&(t+=q.SLIDE_INCREMENT);return t}shouldDisplaySlideshow(){return this.isTagsPage()&&this.getValidSlideCount()>q.VALIDATION_ERRORS_EMPTY}exportConfig(){const e={};for(const[t,i]of this.config)e[t]=i;return JSON.stringify(e,(t,i)=>i,et.INDENT_SIZE)}importConfig(e){try{const t=JSON.parse(e);for(const[i,r]of Object.entries(t))this.config.set(i,r);return!0}catch{return!1}}resetToDefaults(){this.config.clear(),this.loadDefaultConfig()}}class Q{constructor(){this.errorLog=[],this.configManager=J.getInstance(),this.setupGlobalErrorHandler()}static getInstance(){return Q.instance||(Q.instance=new Q),Q.instance}setupGlobalErrorHandler(){globalThis.addEventListener("error",e=>{this.logError(e.error,"Global Error Handler")}),globalThis.addEventListener("unhandledrejection",e=>{this.logError(new Error(e.reason),"Unhandled Promise Rejection")})}logError(e,t="Unknown"){const i={timestamp:new Date,error:e,context:t};this.errorLog.push(i),this.errorLog.length>H.MAX_ERROR_LOG_ENTRIES&&this.errorLog.shift()}handleAsync(e,t,i){return e().then(r=>r,r=>(this.logError(r,t),i))}handleSync(e,t,i){try{return e()}catch(r){return this.logError(r,t),i}}isValidUrl(e){if(!e||typeof e!="string")return!1;try{return!!new URL(e)}catch{return!1}}validateSlideConfig(e,t,i){const r=[];return(!Number.isInteger(e)||e<H.SLIDE_NUMBER_MIN||e>H.SLIDE_NUMBER_MAX)&&r.push(`Slide number must be between ${H.SLIDE_NUMBER_MIN} and ${H.SLIDE_NUMBER_MAX}`),(!t||!this.isValidUrl(t))&&r.push("Image URL is required and must be valid"),i&&!this.isValidUrl(i)&&r.push("Link URL must be valid if provided"),{isValid:r.length===q.VALIDATION_ERRORS_EMPTY,errors:r}}validateElement(e,t="Element validation"){try{const i=document.querySelector(e);if(!i){this.logError(new Error(`Element not found: ${e}`),t);return}return i}catch(i){this.logError(i,t);return}}safeDOMOperation(e,t){return this.handleSync(e,`DOM Operation: ${t}`)}showErrorNotification(e,t="error"){try{R.alerts&&R.alerts.show&&R.alerts.show({type:t,content:e})}catch{}}getErrorLog(){return[...this.errorLog]}clearErrorLog(){this.errorLog=[]}exportErrorLog(){const e=this.errorLog.map(i=>({timestamp:i.timestamp.toISOString(),message:i.error.message,stack:i.error.stack,context:i.context}));let t=JSON.stringify(e);try{const i=JSON.parse(t);t=this.formatJsonWithIndent(i,et.INDENT_SIZE)}catch{}return t}formatJsonWithIndent(e,t){const i=" ".repeat(t);return JSON.stringify(e,(r,n)=>n,i)}checkDependencies(){const e=[];return typeof R>"u"&&e.push("Flarum app object"),{isValid:e.length===q.VALIDATION_ERRORS_EMPTY,missing:e}}validateConfiguration(){const e=[],t=this.configManager.getExtensionConfig();t.extensionId||e.push("Extension ID is not configured");const i=t.maxSlides;(i<H.CONFIG_MAX_SLIDES_MIN||i>H.CONFIG_MAX_SLIDES_MAX)&&e.push(`Max slides should be between ${H.CONFIG_MAX_SLIDES_MIN} and ${H.CONFIG_MAX_SLIDES_MAX}`);const r=t.transitionTime;return(r<H.TRANSITION_TIME_MIN||r>H.TRANSITION_TIME_MAX)&&e.push(`Transition time should be between ${H.TRANSITION_TIME_MIN} and ${H.TRANSITION_TIME_MAX} milliseconds`),{isValid:e.length===0,issues:e}}initialize(){try{const e=this.checkDependencies();if(!e.isValid)return this.logError(new Error(`Missing dependencies: ${e.missing.join(", ")}`),"Dependency Check"),!1;const t=this.validateConfiguration();return t.isValid||this.logError(new Error(`Configuration issues: ${t.issues.join(", ")}`),"Configuration Check"),!0}catch(e){return this.logError(e,"Error Handler Initialization"),!1}}}R.initializers.add(N.app.extensionId,()=>{const s=Q.getInstance(),e=J.getInstance();if(!s.initialize())return;const t=new _i,i=new Ni;he.extend(st.prototype,"view",function(n){s.handleSync(()=>{e.isTagsPage()&&Di(n,t,i)},"HeaderPrimary view extension")}),he.extend(Ne.prototype,"oncreate",function(n){s.handleSync(()=>{setTimeout(()=>{i.changeCategoryLayout()},100)},"TagsPage oncreate extension")}),he.extend(Ne.prototype,"onupdate",function(n){s.handleSync(()=>{document.getElementById("swiperTagContainer")||setTimeout(()=>{i.changeCategoryLayout()},100)},"TagsPage onupdate extension")})});const Di=(s,e,t)=>{try{ki(t);try{e.attachAdvertiseHeader(s)}catch{}R.session.user||Ri()}catch{}},ki=s=>{try{document.getElementById("swiperTagContainer")||s.changeCategoryLayout()}catch{}},Ri=()=>{let s=document.getElementById(N.ui.headerIconId);if(s===null){const e=R.forum.attribute("Client1HeaderAdvHeaderIconUrl")||N.ui.headerIconUrl;s=document.createElement("div"),s.id=N.ui.headerIconId,s.style.display="inline-block",s.style.marginTop="8px",s.innerHTML=`<img src="${e}" style="height: 24px;" />`;const t=document.querySelector("#app-navigation .App-backControl");t&&t.firstChild&&t.firstChild.before(s)}}})(flarum.core.compat["common/extend"],flarum.core.compat["forum/app"],flarum.core.compat["forum/components/HeaderPrimary"],flarum.core.compat["tags/components/TagsPage"],flarum.core.compat["common/utils/extractText"]);
//# sourceMappingURL=forum.js.map

module.exports={};