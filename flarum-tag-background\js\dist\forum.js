(function(c,s,u){"use strict";function a(e){if(!e)return;e.querySelectorAll("li.TagTile").forEach(n=>{d(n)}),e.querySelectorAll(".swiper-slide-tag").forEach(n=>{y(n)})}function d(e){const o=e.querySelector("a.TagTile-info"),t=e.querySelector(".TagTile-name");if(!o)return;const n=i(o.href);if(!n)return;const r=g(n);r?(e.style.background=`url(${r})`,e.style.backgroundSize="cover",e.style.backgroundPosition="center",e.style.backgroundRepeat="no-repeat",t&&(t.style.display="none")):(e.style.background="",e.style.backgroundSize="",e.style.backgroundPosition="",e.style.backgroundRepeat="",t&&(t.style.display=""))}function y(e){const o=e.querySelector("a"),t=e.querySelector(".swiper-slide-tag-inner, .swiper-slide-tag-inner-mobile");if(!o||!t)return;const n=i(o.href);if(!n)return;const r=g(n);if(r){t.style.background=`url(${r})`,t.style.backgroundSize="cover",t.style.backgroundPosition="center",t.style.backgroundRepeat="no-repeat";const l=t.querySelector("div");l&&(l.style.display="none")}else{const l=t.querySelector("div");l&&(l.style.display="")}}function i(e){try{const t=new URL(e,window.location.origin).pathname.split("/").filter(Boolean),n=t.indexOf("t"),r=t.indexOf("tags");return n!==-1&&t[n+1]?t[n+1]:r!==-1&&t[r+1]?t[r+1]:t.length>0?t[t.length-1]:null}catch{return null}}function g(e){try{const t=c.store.all("tags").find(n=>(typeof n.slug=="function"?n.slug():n.attribute&&n.attribute("slug"))===e);return t&&t.attribute?t.attribute("wusong8899BackgroundURL"):null}catch{return null}}c.initializers.add("wusong8899-tag-background",()=>{s.extend(u.prototype,"oncreate",function(e){setTimeout(()=>{const o=e?.dom||document.querySelector(".TagsPage-content");a(o)},150)}),s.extend(u.prototype,"onupdate",function(e){setTimeout(()=>{const o=e?.dom||document.querySelector(".TagsPage-content");a(o)},150)}),document.addEventListener("tagsLayoutChanged",()=>{setTimeout(()=>{const e=document.querySelector(".TagsPage-content");a(e)},50)})})})(flarum.core.compat["forum/app"],flarum.core.compat.extend,flarum.core.compat["tags/components/TagsPage"]);
//# sourceMappingURL=forum.js.map

module.exports={};