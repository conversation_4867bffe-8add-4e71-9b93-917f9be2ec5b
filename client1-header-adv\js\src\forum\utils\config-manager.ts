import app from 'flarum/forum/app';
import extractText from 'flarum/common/utils/extractText';
import { defaultConfig, type RootConfig, type SlideData } from '../../common/config';
import { SLIDESHOW_CONSTANTS, JSON_CONSTANTS } from '../../common/config/constants';

/**
 * Configuration management utility
 */
export class ConfigManager {
    private static instance: ConfigManager;
    private config = new Map<string, unknown>();
    private typedConfig: RootConfig = defaultConfig;

    private constructor() {
        this.loadDefaultConfig();
    }

    /**
     * Get singleton instance
     */
    static getInstance(): ConfigManager {
        if (!ConfigManager.instance) {
            ConfigManager.instance = new ConfigManager();
        }
        return ConfigManager.instance;
    }

    /**
     * Load default configuration
     */
    private loadDefaultConfig(): void {
        this.config.set('maxSlides', defaultConfig.slider.maxSlides);
        this.config.set('defaultTransitionTime', defaultConfig.slider.defaultTransitionTime);
        this.config.set('checkTime', defaultConfig.slider.checkTime);
        this.config.set('dataCheckInterval', defaultConfig.slider.dataCheckInterval);
    }

    /**
     * Get configuration value
     * @param key - Configuration key
     * @param defaultValue - Default value if key not found
     * @returns Configuration value
     */
    get(key: string, defaultValue?: unknown): unknown {
        return this.config.get(key) ?? defaultValue;
    }

    /**
     * Set configuration value
     * @param key - Configuration key
     * @param value - Configuration value
     */
    set(key: string, value: unknown): void {
        this.config.set(key, value);
    }


    /**
     * Safely read a forum attribute if available
     */
    private getForumAttribute(key: string): unknown {
        try {
            const forum = app && app.forum;
            const attrFn = forum && forum.attribute;
            if (typeof attrFn === 'function') {
                return attrFn.call(forum, key);
            }
            return;
        } catch {
            return;
        }
    }

    /**
     * Get transition time from forum settings
     * @returns Transition time in milliseconds
     */
    getTransitionTime(): number {
        const transitionTime = this.getForumAttribute('Client1HeaderAdvTransitionTime');
        if (transitionTime) {
            return Number.parseInt(String(transitionTime), 10);
        }
        return this.get('defaultTransitionTime') as number;
    }

    /**
     * Get slide image URL
     * @param {number} slideNumber - Slide number (1-based)
     * @returns {string | null} Image URL or null if not set
     */
    getSlideImage(slideNumber: number): string {
        const result = this.getForumAttribute(`Client1HeaderAdvImage${slideNumber}`) as string;
        return result || '';
    }

    /**
     * Get slide link URL
     * @param {number} slideNumber - Slide number (1-based)
     * @returns {string} Link URL or empty string if not set
     */
    getSlideLink(slideNumber: number): string {
        const result = this.getForumAttribute(`Client1HeaderAdvLink${slideNumber}`) as string;
        return result || '';
    }

    /**
     * Get all configured slides
     * @returns Array of slide configurations
     */
    getAllSlides(): SlideData[] {
        const slides: SlideData[] = [];
        const maxSlides = this.get('maxSlides') as number;

        for (let slideIndex = SLIDESHOW_CONSTANTS.INITIAL_SLIDE_INDEX; slideIndex <= maxSlides; slideIndex += SLIDESHOW_CONSTANTS.SLIDE_INCREMENT) {
            const image = this.getSlideImage(slideIndex);
            const link = this.getSlideLink(slideIndex);

            if (image) {
                slides.push({
                    slideNumber: slideIndex,
                    image,
                    link: link || '#'
                });
            }
        }

        return slides;
    }

    /**
     * Get header icon URL from forum settings
     * @returns Header icon URL or empty string
     */
    getHeaderIconUrl(): string {
        return String(this.getForumAttribute('Client1HeaderAdvHeaderIconUrl') || '');
    }

    /**
     * Get social media URL for a platform
     * @param platform - Social media platform name
     * @returns Social media URL or empty string
     */
    getSocialMediaUrl(platform: string): string {
        return String(this.getForumAttribute(`wusong8899-client1-header-adv.Social${platform}Url`) || '');
    }

    /**
     * Get social media icon for a platform
     * @param platform - Social media platform name
     * @returns Social media icon or empty string
     */
    getSocialMediaIcon(platform: string): string {
        return String(this.getForumAttribute(`wusong8899-client1-header-adv.Social${platform}Icon`) || '');
    }

    /**
     * Get translation with parameters
     * @param key - Translation key
     * @param parameters - Translation parameters
     * @returns Translated string
     */
    getTranslation(key: string, parameters: Record<string, unknown> = {}): string {
        const fullKey = this.getTranslationKey(key);
        const translator = app && app.translator;
        if (translator && translator.trans) {
            const result = translator.trans(fullKey, parameters);
            return extractText(result);
        }
        return key;
    }

    /**
     * Get full translation key
     * @param key - Short key
     * @returns Full translation key
     */
    private getTranslationKey(key: string): string {
        return `${defaultConfig.app.translationPrefix}.${key}`;
    }

    /**
     * Check if user is logged in
     * @returns True if user is logged in
     */
    isUserLoggedIn(): boolean {
        return Boolean(app && app.session && app.session.user);
    }

    /**
     * Get current route name
     * @returns Current route name or null
     */
    getCurrentRoute(): string {
        const current = app && app.current;
        const routeName = current && current.get && current.get('routeName');
        return routeName || '';
    }

    /**
     * Check if current page is tags page
     * @returns True if on tags page
     */
    isTagsPage(): boolean {
        const route = this.getCurrentRoute();
        const currentPath = (globalThis.location && globalThis.location.pathname) || '';

        // Check both route name and URL path to be more reliable
        return route === 'tags' || route === 'tag' ||
            currentPath.includes('/tags') ||
            currentPath === '/' ||
            document.querySelector('.TagsPage') !== null;
    }

    /**
     * Check if current page is discussion page
     * @returns True if on discussion page
     */
    isDiscussionPage(): boolean {
        const route = this.getCurrentRoute();
        return route === 'discussion';
    }

    /**
     * Check if current page is user page
     * @returns True if on user page
     */
    isUserPage(): boolean {
        const route = this.getCurrentRoute();
        return route === 'user';
    }

    /**
     * Check if a slide is valid (has image)
     * @param slideNumber - Slide number to check
     * @returns True if slide has valid image
     */
    isSlideValid(slideNumber: number): boolean {
        const image = this.getSlideImage(slideNumber);
        return typeof image === 'string' && image.trim().length > SLIDESHOW_CONSTANTS.VALIDATION_ERRORS_EMPTY;
    }

    /**
     * Get count of valid slides
     * @returns Number of valid slides
     */
    getValidSlideCount(): number {
        const maxSlides = this.get('maxSlides') as number;
        let count = SLIDESHOW_CONSTANTS.VALIDATION_ERRORS_EMPTY;

        for (let slideIndex = SLIDESHOW_CONSTANTS.INITIAL_SLIDE_INDEX; slideIndex <= maxSlides; slideIndex += SLIDESHOW_CONSTANTS.SLIDE_INCREMENT) {
            if (this.isSlideValid(slideIndex)) {
                count += SLIDESHOW_CONSTANTS.SLIDE_INCREMENT;
            }
        }

        return count;
    }

    /**
     * Check if slideshow should be displayed
     * @returns True if slideshow should be shown
     */
    shouldDisplaySlideshow(): boolean {
        return this.isTagsPage() && this.getValidSlideCount() > SLIDESHOW_CONSTANTS.VALIDATION_ERRORS_EMPTY;
    }

    /**
     * Export configuration as JSON
     * @returns JSON string of configuration
     */
    exportConfig(): string {
        const configObject: Record<string, unknown> = {};
        for (const [key, value] of this.config) {
            configObject[key] = value;
        }
        return JSON.stringify(configObject, (key, value) => value, JSON_CONSTANTS.INDENT_SIZE);
    }

    /**
     * Import configuration from JSON
     * @param jsonConfig - JSON configuration string
     * @returns True if import successful
     */
    importConfig(jsonConfig: string): boolean {
        try {
            const configObject = JSON.parse(jsonConfig);
            for (const [key, value] of Object.entries(configObject)) {
                this.config.set(key, value);
            }
            return true;
        } catch {
            return false;
        }
    }

    /**
     * Reset configuration to defaults
     */
    resetToDefaults(): void {
        this.config.clear();
        this.loadDefaultConfig();
    }
}
