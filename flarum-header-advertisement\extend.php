<?php

declare(strict_types=1);

use Flarum\Extend;

return [
    (new Extend\Frontend('forum'))
        ->js(__DIR__ . '/js/dist/forum.js')
        ->css(__DIR__ . '/less/forum.less'),

    (new Extend\Frontend('admin'))
        ->js(__DIR__ . '/js/dist/admin.js')
        ->css(__DIR__ . '/less/admin.less'),

    new Extend\Locales(__DIR__ . '/locale'),

    // Basic settings
    (new Extend\Settings())->serializeToForum(
        'wusong8899-flarum-header-advertisement.TransitionTime',
        'wusong8899-flarum-header-advertisement.TransitionTime'
    ),

    (new Extend\Settings())->serializeToForum(
        'wusong8899-flarum-header-advertisement.HeaderIconUrl',
        'wusong8899-flarum-header-advertisement.HeaderIconUrl'
    ),

    // Generate settings for slides (1-30)
    ...array_merge(...array_map(function ($i) {
        return [
            (new Extend\Settings())->serializeToForum(
                "wusong8899-flarum-header-advertisement.Link{$i}",
                "wusong8899-flarum-header-advertisement.Link{$i}"
            ),
            (new Extend\Settings())->serializeToForum(
                "wusong8899-flarum-header-advertisement.Image{$i}",
                "wusong8899-flarum-header-advertisement.Image{$i}"
            ),
        ];
    }, range(1, 30))),
];
