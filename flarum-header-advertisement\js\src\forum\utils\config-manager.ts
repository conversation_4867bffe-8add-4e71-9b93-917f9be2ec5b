import app from 'flarum/forum/app';
import { defaultConfig } from '../../common/config';

/**
 * Configuration manager for the Header Advertisement extension
 */
export class ConfigManager {
    private static instance: ConfigManager;

    private constructor() {
        // Private constructor for singleton pattern
    }

    /**
     * Get singleton instance
     */
    public static getInstance(): ConfigManager {
        if (!ConfigManager.instance) {
            ConfigManager.instance = new ConfigManager();
        }
        return ConfigManager.instance;
    }

    /**
     * Check if current page is tags page
     */
    public isTagsPage(): boolean {
        try {
            const currentRoute = app.current.get('routeName');
            return currentRoute === 'tags';
        } catch {
            // Fallback: check URL
            try {
                return globalThis.location.pathname.includes('/tags');
            } catch {
                return false;
            }
        }
    }

    /**
     * Get extension configuration
     */
    public getConfig(): typeof defaultConfig {
        return defaultConfig;
    }

    /**
     * Check if slideshow is properly configured
     */
    public isSlideshowConfigured(): boolean {
        try {
            const FIRST_SLIDE_INDEX = 1;
            const SLIDE_INCREMENT = 1;
            // Check if at least one slide is configured
            for (let slideIndex = FIRST_SLIDE_INDEX; slideIndex <= defaultConfig.slider.maxSlides; slideIndex += SLIDE_INCREMENT) {
                const image = app.forum.attribute(`FlarumHeaderAdvImage${slideIndex}`);
                if (image) {
                    return true;
                }
            }
            return false;
        } catch {
            return false;
        }
    }
}
