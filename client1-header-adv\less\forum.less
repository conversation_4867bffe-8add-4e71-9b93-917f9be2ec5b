#client1HeaderButton2 {
  display:none;
}

#client1HeaderButton3 {
  display:none;
}

.selectTitleContainer{
  text-align: center;
}

.selectTitle {
    width: 100%;
    overflow-y: hidden;
    overflow-x: hidden;
    padding-top: 10px;
    padding-bottom: 5px;
}

.selectTitle .switch-btns {
    width: auto;
    overflow-x: auto;
    display: inline-block;
    background-color: #1b2132;
    border-radius: 25px;
    padding: 4px;
    -ms-overflow-style: none;  /* Internet Explorer 10+ */
    scrollbar-width: none;  /* Firefox */
}

.selectTitle .switch-btns::-webkit-scrollbar { 
    display: none;  /* Safari and Chrome */
}

.selectTitle .switch-btns .btns-container {
    position: relative;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
}

@media @phone{
  .selectTitle .switch-btns button {
      font-size: 14px !important;
      padding: 6px 6px !important;
  }
}

.selectTitle .switch-btns button {
    color: #f3f3f3;
    padding: 10px 15px;
    background: transparent;
    border-radius: 0;
    font-size: 15px;
    position: relative;
    z-index: 1;
    cursor: pointer;
    white-space: nowrap;
}

.u-btn {
    padding: 0;
    margin: 0;
    text-align: center;
    background: #313952;
    border: 0;
    border-radius: 8px;
    outline: none;
    padding: 4px 12px;
    font-size: 12px;
    color: #f3f3f3;
}

.u-btn-text{
  min-width: 53px;
  display: inline-block;
}

.selectTitle .switch-btns i {
    margin-right: 4px;
    color: gold;
}

@media @phone{
  .selected-bg{
    height: 33px !important;
  }
}

.selected-bg{
  width: 106px;
  height: 43px;
  position: absolute;
  left: 0;
  top: 0;
  background: #2b3248;
  border-radius: 21px;
  z-index: 0;
  opacity: 0;
  -webkit-transition: .2s;
  transition: .2s;
}

.zhiboContainer{
  left: 0px;
  width: 100%;
  display:none;
  margin-top: 0px;
  margin-bottom: -10px;
  font-size: 18px;
  font-weight: bold;
  color: var(--button-color);
}

.zhiboSubContainer{
  text-align: center;
  vertical-align: middle;
  display: flex;
  right: 0px;
  height: 46px;
  background: var(--header-bg);
  width: 100%;
  position: fixed;
  bottom: 0px;
  z-index: 999;
  align-items: center;
  justify-content: center;
}



.youxiContainer{
  display:none;
  justify-content: center;
  align-items: center;
  border: 2px solid var(--button-color);
  border-radius: 12px;
  margin-top: 10px;
  margin-bottom: -10px;
  font-size: 18px;
  font-weight: bold;
  color: var(--button-color);
}

.shangchengContainer{
  display:none;
  justify-content: center;
  align-items: center;
  border: 2px solid var(--button-color);
  border-radius: 12px;
  margin-top: 10px;
  margin-bottom: -10px;
  font-size: 18px;
  font-weight: bold;
  color: var(--button-color);
}

.zhiboIframe{
  width: 100%;
  border: 0;
}

.customButtonIframe{
  width: 100%;
  border: 0;
}

.swiper {
 
}

.swiper-wrapper a:hover, a:visited, a:link, a:active{
    text-decoration: none;
}

.tagSwiper{
  padding-top: 6px !important;
}

.TagTextIcon{
  background-image:url('https://i.mji.rip/2025/08/16/7a705b5aac9042ae3eddbbef9b285e9b.png');
  content: "";
  display: inline-block;
  vertical-align: -3px;
  width: 24px;
  height: 24px;
  line-height: 26px;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: top;
  margin-right: 6px;
}

.TagTextContainer{
  font-size: 14px;
  padding: 0px 0px 10px 0px;
  font-weight: bold;
}



.swiperTagContainer{
  margin-top: 10px;
  margin-bottom: -10px;
  padding-bottom: 50px;
}

.swiperAdContainer{
  margin-top: 10px;
  margin-bottom: -10px;
}

.swiper-slide {
  height: 280px;
  width: 600px;
}

.swiper-slide img {
  display: block;
  width: 100%;
  height: 100%;
  border-radius:12px;
}

.swiper-slide-tag {
  -webkit-transition: all .23s linear;
  transition: all .23s linear;
}

@media @desktop-hd{
  .swiper-slide-tag:hover {
    /* opacity: .6; */
    -webkit-transform: translateY(-6px);
    transform: translateY(-6px);
  }
}

.TagTextOuterContainer{

  background:#141623;
  padding: 10px;
  border-radius: 1rem;

}

.swiper-slide-tag-inner {
  border-radius: 12px;
  width:146px;
  height:196px;
  text-align: center;
  padding: 15px;
  display: flex;
  align-items: center;
  justify-content: center;

  /* Ensure background image covers the entire area when no text */
  &:empty {
    padding: 0;
  }
}

.swiper-slide-tag-inner-mobile {
  border-radius: 12px;
  width: 200px;
  height: 240px;
  text-align: center;
  padding: 10px;
  display: flex;
  align-items: center;
  justify-content: center;

  /* Ensure background image covers the entire area when no text */
  &:empty {
    padding: 0;
  }
}

.swiper-pagination{
  transform: translateY(100%) !important;
  line-height: 0;
}

.buttonRegister{
  width: 74px;
  margin-right: 0;
  font-size: 14px;
  -webkit-animation: shockwaveJump 1s ease-out infinite;
  animation: shockwaveJump 1s ease-out infinite;
  height: 30px;
  color: #2b3248;
  background: #FACA46;
  border-radius: 32px;
  text-align: center;
  font-size: 16px;
  border: none;
  padding: 0;
  position: relative;
  outline: none;
  margin-top: -5px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.buttonRegister:before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    border-radius: 16px;
    -webkit-animation: shockwave 1s 0.5s ease-out infinite;
    animation: shockwave 1s 0.5s ease-out infinite;
}

.buttonRegister:after {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    border-radius: 16px;
    -webkit-animation: shockwave 1s 0.65s ease-out infinite;
    animation: shockwave 1s 0.65s ease-out infinite;
}

@-webkit-keyframes shockwaveJump {
  0% {
    -webkit-transform: scale(0.4);
            transform: scale(0.4);
  }
  40% {
    -webkit-transform: scale(1.08);
            transform: scale(1.08);
  }
  50% {
    -webkit-transform: scale(0.98);
            transform: scale(0.98);
  }
  55% {
    -webkit-transform: scale(1.02);
            transform: scale(1.02);
  }
  60% {
    -webkit-transform: scale(0.98);
            transform: scale(0.98);
  }
  100% {
    -webkit-transform: scale(1);
            transform: scale(1);
  }
}
@keyframes shockwaveJump {
  0% {
    -webkit-transform: scale(1);
            transform: scale(1);
  }
  40% {
    -webkit-transform: scale(1.08);
            transform: scale(1.08);
  }
  50% {
    -webkit-transform: scale(0.98);
            transform: scale(0.98);
  }
  55% {
    -webkit-transform: scale(1.02);
            transform: scale(1.02);
  }
  60% {
    -webkit-transform: scale(0.98);
            transform: scale(0.98);
  }
  100% {
    -webkit-transform: scale(1);
            transform: scale(1);
  }
}
@-webkit-keyframes shockwave {
  0% {
    -webkit-transform: scale(1);
            transform: scale(1);
    box-shadow: 0 0 2px rgba(255, 255, 255, 0.15), inset 0 0 2px rgba(255, 255, 255, 0.15);
  }
  95% {
    box-shadow: 0 0 4px #ffffff, inset 0 0 4px #ffffff;
  }
  100% {
    -webkit-transform: scale(1.25);
            transform: scale(1.25);
  }
}
@keyframes shockwave {
  0% {
    -webkit-transform: scale(1);
            transform: scale(1);
    box-shadow: 0 0 2px rgba(250, 202, 70, 0.65), inset 0 0 1px rgba(250, 202, 70, 0.95);
  }
  95% {
    box-shadow: 0 0 16px rgba(250, 202, 70, 0.15), inset 0 0 16px rgba(250, 202, 70, 0.15);
  }
  100% {
    -webkit-transform: scale(1.3);
            transform: scale(1.3);
  }
}

.swiper-button-prev {
    left: calc(~"50% - 280px") !important;
    background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADwAAAA8CAYAAAA6/NlyAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAABEZVhJZk1NACoAAAAIAAGHaQAEAAAAAQAAABoAAAAAAAOgAQADAAAAAQABAACgAgAEAAAAAQAAADygAwAEAAAAAQAAADwAAAAAi9/pXAAAA0BJREFUaEPtmktrU0EUgJPYKkIea5du/QfqwqVuRCQgFopi8dGKVMVSRGnBikKlQlIfrQ+UqlhBBIsrf4BFf4U793mASmvid3pPIHrvTcA7MzfIfHA5M6eL8uWcmTuZNuPxeDwej8fj8ZghqzE1Go3Gnna7PclwF896oVCoZrPZH1s/tECqwsgeQ/Ylz3ZNZZD9hPQB4i9NGSWn0Tm1Wm0U0dfdsgLz/XwQh3VqnFSE6/X6GBVcQW6bpv6A/G4dGse5MNWbIDxFqtfvXtdoHKfCtPHFVqv1ENnYvSOXyy2WSqUvOjWOM2HaeJpQCWaxLOXz+Us6toITYdp4lqrO6zQSKluhsudZ221NWcG6MJW9RRvf0GkkSM7zKrqsU6tYFUZ2gcpe02kcc8Vi8aqOrWPl4CGbErKLDC8EmWio7HVkb+vUCcaFRZY1u0w8q6lIkJ1C9q5OnWFUGMkcss+IJzUVQjYlnknW7H1NOcWYMJJDtPELhiNBJozIEs5R2SdBxj1GhJEdprKrxLKmQiDbIowhuxJk0iGxMJI7kH1LjD3wI7vJe/YEh4pVTaVGImGRpY3fMzwUZMIgu8Ezwpp9p6lUSfQeprJyoOgl+5NQHhRZIVGFqe43qiw3FSGQ/U4bH6WNP2pqIHD25WFQSCosr6FIqPxOztBrdIG124t/wcSmtcbwYJAJ819tWrIp8V49QvygqRB8KMM8b5rNZuyBxCWJ17BIU70yMbaCCA/R3q/ohtgjpyuMbFrIbiB9nGHswQJp+V3PkT4TZNLBiLCA9CbtPUqMPToiLXvGI97fPb822sSYsIBsi0qfIj7WVAiRpr3vUekrmnKKUWEB2TbS4wx7fv1DfAHpfrchxkn85aEXCMkVT79K3iyVSrM6to7xCnfDmp6i4v2ucGb4YHreaJrEaoU7sEnNsG7ndBoJ5+4qS8HqnbTgRFigitO0d79KLtMVVu+mrbZ0N4jcIfSr4DgfTFXHVnBW4Q609wSVfiCvJ01FsZeN7LOOjeKswh1Yp0uE0/LODjJh+Nk+HRrHubBAe29d5SIW91f+rxqN47ylu6G9nf/LQ6rCAtJ//1NLBVm5C/N4PB6Px+PxeAaXTOY3/ptWEo03PGQAAAAASUVORK5CYII=);
    width: 16px !important;
    height: 16px !important;
    transform: translate(-50%,-50%);
    background-size: 100% 100%;
}

.swiper-button-next {
    right: calc(~"50% - 295px") !important;
    background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADwAAAA8CAYAAAA6/NlyAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAABEZVhJZk1NACoAAAAIAAGHaQAEAAAAAQAAABoAAAAAAAOgAQADAAAAAQABAACgAgAEAAAAAQAAADygAwAEAAAAAQAAADwAAAAAi9/pXAAAA0BJREFUaEPtmktrU0EUgJPYKkIea5du/QfqwqVuRCQgFopi8dGKVMVSRGnBikKlQlIfrQ+UqlhBBIsrf4BFf4U793mASmvid3pPIHrvTcA7MzfIfHA5M6eL8uWcmTuZNuPxeDwej8fj8ZghqzE1Go3Gnna7PclwF896oVCoZrPZH1s/tECqwsgeQ/Ylz3ZNZZD9hPQB4i9NGSWn0Tm1Wm0U0dfdsgLz/XwQh3VqnFSE6/X6GBVcQW6bpv6A/G4dGse5MNWbIDxFqtfvXtdoHKfCtPHFVqv1ENnYvSOXyy2WSqUvOjWOM2HaeJpQCWaxLOXz+Us6toITYdp4lqrO6zQSKluhsudZ221NWcG6MJW9RRvf0GkkSM7zKrqsU6tYFUZ2gcpe02kcc8Vi8aqOrWPl4CGbErKLDC8EmWio7HVkb+vUCcaFRZY1u0w8q6lIkJ1C9q5OnWFUGMkcss+IJzUVQjYlnknW7H1NOcWYMJJDtPELhiNBJozIEs5R2SdBxj1GhJEdprKrxLKmQiDbIowhuxJk0iGxMJI7kH1LjD3wI7vJe/YEh4pVTaVGImGRpY3fMzwUZMIgu8Ezwpp9p6lUSfQeprJyoOgl+5NQHhRZIVGFqe43qiw3FSGQ/U4bH6WNP2pqIHD25WFQSCosr6FIqPxOztBrdIG124t/wcSmtcbwYJAJ819tWrIp8V49QvygqRB8KMM8b5rNZuyBxCWJ17BIU70yMbaCCA/R3q/ohtgjpyuMbFrIbiB9nGHswQJp+V3PkT4TZNLBiLCA9CbtPUqMPToiLXvGI97fPb822sSYsIBsi0qfIj7WVAiRpr3vUekrmnKKUWEB2TbS4wx7fv1DfAHpfrchxkn85aEXCMkVT79K3iyVSrM6to7xCnfDmp6i4v2ucGb4YHreaJrEaoU7sEnNsG7ndBoJ5+4qS8HqnbTgRFigitO0d79KLtMVVu+mrbZ0N4jcIfSr4DgfTFXHVnBW4Q609wSVfiCvJ01FsZeN7LOOjeKswh1Yp0uE0/LODjJh+Nk+HRrHubBAe29d5SIW91f+rxqN47ylu6G9nf/LQ6rCAtJ//1NLBVm5C/N4PB6Px+PxeAaXTOY3/ptWEo03PGQAAAAASUVORK5CYII=);
    transform: translate(-50%,-50%) rotateY(180deg);
    width: 16px !important;
    height: 16px !important;
    background-size: 100% 100%;
}

@media @phone {
  .swiper {
    height: 100%;
  }

  .swiperContainer{
    /* width: 700px; */
    /* margin-left: -178px; */
  }

  .swiper-slide {
    /* width: calc(~"100% - 32px") !important; */
  }

  .swiper-slide img {
    width: 100%;
    height: 100%;
    border-radius:12px;
  }
}

.swiper-slide-shadow-right{
  border-radius:12px;
}

.swiper-slide-shadow-left{
  border-radius:12px;
}

/**
 * Swiper 8.4.5
 * Most modern mobile touch slider and framework with hardware accelerated transitions
 * https://swiperjs.com
 *
 * Copyright 2014-2022 Vladimir Kharlampidi
 *
 * Released under the MIT License
 *
 * Released on: November 21, 2022
 */

@themeColor: #007aff;

@font-face {
  font-family: 'swiper-icons';
  src: url('https://cdn.jsdelivr.net/npm/swiper@8/swiper-bundle.min.css');
  font-weight: 400;
  font-style: normal;
}

:root {
  --swiper-theme-color: @themeColor;
}
.swiper {
  margin-left: auto;
  margin-right: auto;
  position: relative;
  overflow: hidden;
  list-style: none;
  padding: 0;
  /* Fix of Webkit flickering */
  z-index: 1;
}
.swiper-vertical > .swiper-wrapper {
  flex-direction: column;
}
.swiper-wrapper {
  position: relative;
  width: 100%;
  height: 100%;
  z-index: 1;
  display: flex;
  transition-property: transform;
  box-sizing: content-box;
}
.swiper-android .swiper-slide,
.swiper-wrapper {
  transform: translate3d(0px, 0, 0);
}

.swiper-pointer-events {
  touch-action: pan-y;
  &.swiper-vertical {
    touch-action: pan-x;
  }
}
.swiper-slide {
  flex-shrink: 0;
  width: 100%;
  height: 100%;
  position: relative;
  transition-property: transform;
}
.swiper-slide-invisible-blank {
  visibility: hidden;
}
/* Auto Height */
.swiper-autoheight,
.swiper-autoheight .swiper-slide {
  height: auto;
}
.swiper-autoheight .swiper-wrapper {
  align-items: flex-start;
  transition-property: transform, height;
}
.swiper-backface-hidden .swiper-slide {
  transform: translateZ(0);
  backface-visibility: hidden;
}
/* 3D Effects */
.swiper-3d {
  &,
  &.swiper-css-mode .swiper-wrapper {
    perspective: 1200px;
  }
  .swiper-wrapper,
  .swiper-slide,
  .swiper-slide-shadow,
  .swiper-slide-shadow-left,
  .swiper-slide-shadow-right,
  .swiper-slide-shadow-top,
  .swiper-slide-shadow-bottom,
  .swiper-cube-shadow {
    transform-style: preserve-3d;
  }
  .swiper-slide-shadow,
  .swiper-slide-shadow-left,
  .swiper-slide-shadow-right,
  .swiper-slide-shadow-top,
  .swiper-slide-shadow-bottom {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 10;
  }
  .swiper-slide-shadow {
    background: rgba(0, 0, 0, 0.15);
  }
  .swiper-slide-shadow-left {
    background-image: linear-gradient(to left, rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0));
  }
  .swiper-slide-shadow-right {
    background-image: linear-gradient(to right, rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0));
  }
  .swiper-slide-shadow-top {
    background-image: linear-gradient(to top, rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0));
  }
  .swiper-slide-shadow-bottom {
    background-image: linear-gradient(to bottom, rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0));
  }
}

/* CSS Mode */
.swiper-css-mode {
  > .swiper-wrapper {
    overflow: auto;
    scrollbar-width: none; /* For Firefox */
    -ms-overflow-style: none; /* For Internet Explorer and Edge */
    &::-webkit-scrollbar {
      display: none;
    }
  }
  > .swiper-wrapper > .swiper-slide {
    scroll-snap-align: start start;
  }
}
.swiper-horizontal.swiper-css-mode {
  > .swiper-wrapper {
    scroll-snap-type: x mandatory;
  }
}
.swiper-vertical.swiper-css-mode {
  > .swiper-wrapper {
    scroll-snap-type: y mandatory;
  }
}
.swiper-centered {
  > .swiper-wrapper::before {
    content: '';
    flex-shrink: 0;
    order: 9999;
  }
  &.swiper-horizontal {
    > .swiper-wrapper > .swiper-slide:first-child {
      margin-inline-start: var(--swiper-centered-offset-before);
    }
    > .swiper-wrapper::before {
      height: 100%;
      min-height: 1px;
      width: var(--swiper-centered-offset-after);
    }
  }
  &.swiper-vertical {
    > .swiper-wrapper > .swiper-slide:first-child {
      margin-block-start: var(--swiper-centered-offset-before);
    }
    > .swiper-wrapper::before {
      width: 100%;
      min-width: 1px;
      height: var(--swiper-centered-offset-after);
    }
  }

  > .swiper-wrapper > .swiper-slide {
    scroll-snap-align: center center;
  }
}

@themeColor: #007aff;

:root {
}
.swiper-pagination {
  position: absolute;
  text-align: center;
  transition: 300ms opacity;
  transform: translate3d(0, 0, 0);
  z-index: 10;
  &.swiper-pagination-hidden {
    opacity: 0;
  }
  .swiper-pagination-disabled > &,
  &.swiper-pagination-disabled {
    display: none !important;
  }
}
/* Common Styles */
.swiper-pagination-fraction,
.swiper-pagination-custom,
.swiper-horizontal > .swiper-pagination-bullets,
.swiper-pagination-bullets.swiper-pagination-horizontal {
  bottom: 10px;
  left: 0;
  width: 100%;
}
/* Bullets */
.swiper-pagination-bullets-dynamic {
  overflow: hidden;
  font-size: 0;
  .swiper-pagination-bullet {
    transform: scale(0.33);
    position: relative;
  }
  .swiper-pagination-bullet-active {
    transform: scale(1);
  }
  .swiper-pagination-bullet-active-main {
    transform: scale(1);
  }
  .swiper-pagination-bullet-active-prev {
    transform: scale(0.66);
  }
  .swiper-pagination-bullet-active-prev-prev {
    transform: scale(0.33);
  }
  .swiper-pagination-bullet-active-next {
    transform: scale(0.66);
  }
  .swiper-pagination-bullet-active-next-next {
    transform: scale(0.33);
  }
}
.swiper-pagination-bullet {
  display: inline-block;
  border-radius: 50%;
  height: 5px;
  width: 5px;
  opacity: .5;
  background: #f3f3f3;
  button& {
    border: none;
    margin: 0;
    padding: 0;
    box-shadow: none;
    appearance: none;
  }
  .swiper-pagination-clickable & {
    cursor: pointer;
  }

  &:only-child {
    display: none !important;
  }
}
.swiper-pagination-bullet-active {
  opacity: var(--swiper-pagination-bullet-opacity, 1);
  width: 22px;
  border-radius: 2.5px;
  opacity: 1;
}

.swiper-vertical > .swiper-pagination-bullets,
.swiper-pagination-vertical.swiper-pagination-bullets {
  right: 10px;
  top: 50%;
  transform: translate3d(0px, -50%, 0);
  .swiper-pagination-bullet {
    margin: var(--swiper-pagination-bullet-vertical-gap, 6px) 0;
    display: block;
  }
  &.swiper-pagination-bullets-dynamic {
    top: 50%;
    transform: translateY(-50%);
    width: 8px;
    .swiper-pagination-bullet {
      display: inline-block;
      transition: 200ms transform, 200ms top;
    }
  }
}
.swiper-horizontal > .swiper-pagination-bullets,
.swiper-pagination-horizontal.swiper-pagination-bullets {
  .swiper-pagination-bullet {
    margin: 0 var(--swiper-pagination-bullet-horizontal-gap, 4px);
  }
  &.swiper-pagination-bullets-dynamic {
    left: 50%;
    transform: translateX(-50%);
    white-space: nowrap;
    .swiper-pagination-bullet {
      transition: 200ms transform, 200ms left;
    }
  }
}
.swiper-horizontal.swiper-rtl > .swiper-pagination-bullets-dynamic .swiper-pagination-bullet {
  transition: 200ms transform, 200ms right;
}
/* Progress */
.swiper-pagination-progressbar {
  background: rgba(0, 0, 0, 0.25);
  position: absolute;
  .swiper-pagination-progressbar-fill {
    background: var(--swiper-pagination-color, var(--swiper-theme-color));
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    transform: scale(0);
    transform-origin: left top;
  }
  .swiper-rtl & .swiper-pagination-progressbar-fill {
    transform-origin: right top;
  }
  .swiper-horizontal > &,
  &.swiper-pagination-horizontal,
  .swiper-vertical > &.swiper-pagination-progressbar-opposite,
  &.swiper-pagination-vertical.swiper-pagination-progressbar-opposite {
    width: 100%;
    height: 4px;
    left: 0;
    top: 0;
  }
  .swiper-vertical > &,
  &.swiper-pagination-vertical,
  .swiper-horizontal > &.swiper-pagination-progressbar-opposite,
  &.swiper-pagination-horizontal.swiper-pagination-progressbar-opposite {
    width: 4px;
    height: 100%;
    left: 0;
    top: 0;
  }
}
.swiper-pagination-lock {
  display: none;
}
:root {
  --swiper-navigation-size: 44px;
  --swiper-navigation-color: var(--swiper-theme-color);
}
.swiper-button-prev,
.swiper-button-next {
  position: absolute;
  top: 50%;
  width: calc(~"var(--swiper-navigation-size) / 44 * 27");
  height: var(--swiper-navigation-size);
  margin-top: calc(~"0px - (var(--swiper-navigation-size) / 2)");
  z-index: 10;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--swiper-navigation-color, var(--swiper-theme-color));
  &.swiper-button-disabled {
    opacity: 0.35;
    cursor: auto;
    pointer-events: none;
  }
  &.swiper-button-hidden {
    opacity: 0;
    cursor: auto;
    pointer-events: none;
  }
  .swiper-navigation-disabled & {
    display: none !important;
  }
  &:after {
    font-family: swiper-icons;
    font-size: var(--swiper-navigation-size);
    text-transform: none !important;
    letter-spacing: 0;
    font-variant: initial;
    line-height: 1;
  }
}
.swiper-button-prev,
.swiper-rtl .swiper-button-next {
  &:after {
    /* content: 'prev'; */
  }
  left: 10px;
  right: auto;
}
.swiper-button-next,
.swiper-rtl .swiper-button-prev {
  &:after {
    /* content: 'next'; */
  }
  right: 10px;
  left: auto;
}
.swiper-button-lock {
  display: none;
}
