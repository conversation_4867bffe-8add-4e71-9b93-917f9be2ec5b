{"version": 3, "file": "forum.js", "sources": ["../src/forum/index.ts"], "sourcesContent": ["import app from 'flarum/forum/app';\nimport { extend } from 'flarum/extend';\nimport TagsPage from 'flarum/tags/components/TagsPage';\n\nfunction applyTagBackgrounds(root: Element | null) {\n  if (!root) return;\n\n  // Handle original TagTiles (if they exist)\n  const originalTiles = root.querySelectorAll('li.TagTile');\n  originalTiles.forEach((tile) => {\n    applyBackgroundToTagTile(tile as HTMLElement);\n  });\n\n  // Handle swiper-based layout from client1-header-adv extension\n  const swiperSlides = root.querySelectorAll('.swiper-slide-tag');\n  swiperSlides.forEach((slide) => {\n    applyBackgroundToSwiperSlide(slide as HTMLElement);\n  });\n}\n\nfunction applyBackgroundToTagTile(li: HTMLElement) {\n  const link = li.querySelector('a.TagTile-info') as HTMLAnchorElement | null;\n  const nameEl = li.querySelector('.TagTile-name') as HTMLElement | null;\n\n  if (!link) return;\n\n  const slug = extractSlugFromUrl(link.href);\n  if (!slug) return;\n\n  const bgUrl = getTagBackgroundUrl(slug);\n\n  if (bgUrl) {\n    li.style.background = `url(${bgUrl})`;\n    li.style.backgroundSize = 'cover';\n    li.style.backgroundPosition = 'center';\n    li.style.backgroundRepeat = 'no-repeat';\n    if (nameEl) {\n      // Maintain existing behavior: hide name when a background image is set\n      nameEl.style.display = 'none';\n    }\n  } else {\n    // Reset to default behavior that uses --tag-bg (set by Flarum core)\n    li.style.background = '';\n    li.style.backgroundSize = '';\n    li.style.backgroundPosition = '';\n    li.style.backgroundRepeat = '';\n    if (nameEl) {\n      nameEl.style.display = '';\n    }\n  }\n}\n\nfunction applyBackgroundToSwiperSlide(slide: HTMLElement) {\n  const link = slide.querySelector('a') as HTMLAnchorElement | null;\n  const innerDiv = slide.querySelector('.swiper-slide-tag-inner, .swiper-slide-tag-inner-mobile') as HTMLElement | null;\n\n  if (!link || !innerDiv) return;\n\n  const slug = extractSlugFromUrl(link.href);\n  if (!slug) return;\n\n  const bgUrl = getTagBackgroundUrl(slug);\n\n  if (bgUrl) {\n    // Apply background to the inner div\n    innerDiv.style.background = `url(${bgUrl})`;\n    innerDiv.style.backgroundSize = 'cover';\n    innerDiv.style.backgroundPosition = 'center';\n    innerDiv.style.backgroundRepeat = 'no-repeat';\n\n    // Hide text content when background image is present\n    const textContent = innerDiv.querySelector('div');\n    if (textContent) {\n      textContent.style.display = 'none';\n    }\n  } else {\n    // Reset to default behavior\n    const textContent = innerDiv.querySelector('div');\n    if (textContent) {\n      textContent.style.display = '';\n    }\n  }\n}\n\nfunction extractSlugFromUrl(href: string): string | null {\n  try {\n    const url = new URL(href, window.location.origin);\n    // Flarum tags routes are typically /t/:slug or /tags/:slug\n    const parts = url.pathname.split('/').filter(Boolean);\n    const tIndex = parts.indexOf('t');\n    const tagsIndex = parts.indexOf('tags');\n\n    if (tIndex !== -1 && parts[tIndex + 1]) return parts[tIndex + 1];\n    if (tagsIndex !== -1 && parts[tagsIndex + 1]) return parts[tagsIndex + 1];\n    if (parts.length > 0) return parts[parts.length - 1];\n\n    return null;\n  } catch {\n    return null;\n  }\n}\n\nfunction getTagBackgroundUrl(slug: string): string | null {\n  try {\n    // Find tag by slug in the store\n    // @ts-ignore types provided by flarum/tags\n    const tags = app.store.all('tags') as any[];\n    const model = tags.find((t) => (typeof t.slug === 'function' ? t.slug() : t.attribute && t.attribute('slug')) === slug);\n\n    if (!model) return null;\n\n    return model.attribute ? model.attribute('wusong8899BackgroundURL') : null;\n  } catch {\n    return null;\n  }\n}\n\napp.initializers.add('wusong8899-tag-background', () => {\n  extend(TagsPage.prototype, 'oncreate', function (this: any, vnode?: any) {\n    // Add delay to ensure DOM is fully rendered and other extensions have processed\n    setTimeout(() => {\n      const rootElement = vnode?.dom || document.querySelector('.TagsPage-content');\n      applyTagBackgrounds(rootElement);\n    }, 150); // Increased delay to ensure client1-header-adv has processed first\n  });\n\n  extend(TagsPage.prototype, 'onupdate', function (this: any, vnode?: any) {\n    // Add delay to ensure DOM is fully updated and other extensions have processed\n    setTimeout(() => {\n      const rootElement = vnode?.dom || document.querySelector('.TagsPage-content');\n      applyTagBackgrounds(rootElement);\n    }, 150); // Increased delay to ensure client1-header-adv has processed first\n  });\n\n  // Also listen for custom events that might be triggered by other extensions\n  document.addEventListener('tagsLayoutChanged', () => {\n    setTimeout(() => {\n      const rootElement = document.querySelector('.TagsPage-content');\n      applyTagBackgrounds(rootElement);\n    }, 50);\n  });\n});\n\n"], "names": ["applyTagBackgrounds", "root", "tile", "applyBackgroundToTagTile", "slide", "applyBackgroundToSwiperSlide", "li", "link", "nameEl", "slug", "extractSlugFromUrl", "bgUrl", "getTagBackgroundUrl", "innerDiv", "textContent", "href", "parts", "tIndex", "tagsIndex", "model", "app", "t", "extend", "TagsPage", "vnode", "rootElement"], "mappings": "8BAIA,SAASA,EAAoBC,EAAsB,CACjD,GAAI,CAACA,EAAM,OAGWA,EAAK,iBAAiB,YAAY,EAC1C,QAASC,GAAS,CAC9BC,EAAyBD,CAAmB,CAC9C,CAAC,EAGoBD,EAAK,iBAAiB,mBAAmB,EACjD,QAASG,GAAU,CAC9BC,EAA6BD,CAAoB,CACnD,CAAC,CACH,CAEA,SAASD,EAAyBG,EAAiB,CACjD,MAAMC,EAAOD,EAAG,cAAc,gBAAgB,EACxCE,EAASF,EAAG,cAAc,eAAe,EAE/C,GAAI,CAACC,EAAM,OAEX,MAAME,EAAOC,EAAmBH,EAAK,IAAI,EACzC,GAAI,CAACE,EAAM,OAEX,MAAME,EAAQC,EAAoBH,CAAI,EAElCE,GACFL,EAAG,MAAM,WAAa,OAAOK,CAAK,IAClCL,EAAG,MAAM,eAAiB,QAC1BA,EAAG,MAAM,mBAAqB,SAC9BA,EAAG,MAAM,iBAAmB,YACxBE,IAEFA,EAAO,MAAM,QAAU,UAIzBF,EAAG,MAAM,WAAa,GACtBA,EAAG,MAAM,eAAiB,GAC1BA,EAAG,MAAM,mBAAqB,GAC9BA,EAAG,MAAM,iBAAmB,GACxBE,IACFA,EAAO,MAAM,QAAU,IAG7B,CAEA,SAASH,EAA6BD,EAAoB,CACxD,MAAMG,EAAOH,EAAM,cAAc,GAAG,EAC9BS,EAAWT,EAAM,cAAc,yDAAyD,EAE9F,GAAI,CAACG,GAAQ,CAACM,EAAU,OAExB,MAAMJ,EAAOC,EAAmBH,EAAK,IAAI,EACzC,GAAI,CAACE,EAAM,OAEX,MAAME,EAAQC,EAAoBH,CAAI,EAEtC,GAAIE,EAAO,CAETE,EAAS,MAAM,WAAa,OAAOF,CAAK,IACxCE,EAAS,MAAM,eAAiB,QAChCA,EAAS,MAAM,mBAAqB,SACpCA,EAAS,MAAM,iBAAmB,YAGlC,MAAMC,EAAcD,EAAS,cAAc,KAAK,EAC5CC,IACFA,EAAY,MAAM,QAAU,OAEhC,KAAO,CAEL,MAAMA,EAAcD,EAAS,cAAc,KAAK,EAC5CC,IACFA,EAAY,MAAM,QAAU,GAEhC,CACF,CAEA,SAASJ,EAAmBK,EAA6B,CACvD,GAAI,CAGF,MAAMC,EAFM,IAAI,IAAID,EAAM,OAAO,SAAS,MAAM,EAE9B,SAAS,MAAM,GAAG,EAAE,OAAO,OAAO,EAC9CE,EAASD,EAAM,QAAQ,GAAG,EAC1BE,EAAYF,EAAM,QAAQ,MAAM,EAEtC,OAAIC,IAAW,IAAMD,EAAMC,EAAS,CAAC,EAAUD,EAAMC,EAAS,CAAC,EAC3DC,IAAc,IAAMF,EAAME,EAAY,CAAC,EAAUF,EAAME,EAAY,CAAC,EACpEF,EAAM,OAAS,EAAUA,EAAMA,EAAM,OAAS,CAAC,EAE5C,IACT,MAAQ,CACN,OAAO,IACT,CACF,CAEA,SAASJ,EAAoBH,EAA6B,CACxD,GAAI,CAIF,MAAMU,EADOC,EAAI,MAAM,IAAI,MAAM,EACd,KAAMC,IAAO,OAAOA,EAAE,MAAS,WAAaA,EAAE,KAAA,EAASA,EAAE,WAAaA,EAAE,UAAU,MAAM,KAAOZ,CAAI,EAEtH,OAAKU,GAEEA,EAAM,UAAYA,EAAM,UAAU,yBAAyB,EAF/C,IAGrB,MAAQ,CACN,OAAO,IACT,CACF,CAEAC,EAAI,aAAa,IAAI,4BAA6B,IAAM,CACtDE,EAAAA,OAAOC,EAAS,UAAW,WAAY,SAAqBC,EAAa,CAEvE,WAAW,IAAM,CACf,MAAMC,EAAcD,GAAO,KAAO,SAAS,cAAc,mBAAmB,EAC5ExB,EAAoByB,CAAW,CACjC,EAAG,GAAG,CACR,CAAC,EAEDH,EAAAA,OAAOC,EAAS,UAAW,WAAY,SAAqBC,EAAa,CAEvE,WAAW,IAAM,CACf,MAAMC,EAAcD,GAAO,KAAO,SAAS,cAAc,mBAAmB,EAC5ExB,EAAoByB,CAAW,CACjC,EAAG,GAAG,CACR,CAAC,EAGD,SAAS,iBAAiB,oBAAqB,IAAM,CACnD,WAAW,IAAM,CACf,MAAMA,EAAc,SAAS,cAAc,mBAAmB,EAC9DzB,EAAoByB,CAAW,CACjC,EAAG,EAAE,CACP,CAAC,CACH,CAAC"}